import assert from 'assert'

import { BadRequest } from '@feathersjs/errors'

import { app } from '../../../src/app'
import type { Tournament } from '../../../src/services/tournaments/tournaments.schema';
import { createTestOrganizer } from '../../fixtures/create-test-organizer'

describe('Match-Tournament validation', () => {
  const matchesService = app.service('matches')
  const tournamentsService = app.service('tournaments')
  const usersService = app.service('users')

  let user: any
  let organizer: any
  let tournament: Tournament // Updated type
  let userParams: any

  before(async () => {
    // Create a test user
    user = await usersService.create({
      email: `test-match-tournament-${Date.now()}@example.com`,
      password: 'supersecret'
    })

    // Create authentication params
    const { accessToken } = await app.service('authentication').create({
      strategy: 'local',
      email: user.email,
      password: 'supersecret'
    })

    userParams = {
      provider: 'rest',
      authentication: {
        strategy: 'jwt',
        accessToken
      },
      user
    }

    // Create a test organizer
    organizer = await createTestOrganizer(user.id, userParams)

    // Create a test tournament
    tournament = await tournamentsService.create({
      name: 'Test Tournament for Match Validation',
      description: 'A tournament for testing match validation',
      organizerId: organizer.id
    }, userParams)
  })

  after(async () => {
    // Clean up in the correct order due to foreign key constraints
    try {
      await tournamentsService.remove(tournament.id, userParams)
      await app.service('organizers').remove(organizer.id, userParams)
      await usersService.remove(user.id)
    } catch (error) {
      console.error('Error cleaning up test data:', error)
    }
  })

  it('validates that the tournament exists', async () => {
    try {
      // Try to create a match with a non-existent tournament
      await matchesService.create({
        name: 'Test Match With Invalid Tournament',
        isActive: true,
        description: 'A test match that should fail validation',
        styleDivisions: JSON.stringify(['recurve', 'compound']),
        ageDivisions: JSON.stringify(['senior', 'junior']),
        forMen: true,
        forWomen: true,
        maxPlayersAmount: 10,
        organizerId: organizer.id,
        tournamentId: 99999 // Non-existent tournament ID
      }, userParams)
      assert.fail('Should not allow creation with a non-existent tournament')
    } catch (error: any) {
      assert.ok(error instanceof BadRequest, 'Throws BadRequest for non-existent tournament')
      assert.ok(error.message.includes('tournament does not exist'), 'Error message mentions tournament existence')
    }
  })

  it('successfully creates a match with a valid tournament', async () => {
    try {
      // Create a match with a valid tournament
      const match = await matchesService.create({
        name: 'Test Match With Valid Tournament',
        isActive: true,
        description: 'A test match that should pass validation',
        styleDivisions: JSON.stringify(['recurve', 'compound']),
        ageDivisions: JSON.stringify(['senior', 'junior']),
        forMen: true,
        forWomen: true,
        maxPlayersAmount: 10,
        organizerId: organizer.id,
        tournamentId: tournament.id // Valid tournament ID
      }, userParams)

      assert.ok(match, 'Created a match with a valid tournament')
      assert.strictEqual(match.tournamentId, tournament.id, 'Match has the correct tournament ID')

      // Clean up the match
      await matchesService.remove(match.id, userParams)
    } catch (error) {
      assert.fail(`Should allow creation with a valid tournament: ${error}`)
    }
  })

  it('allows creating a match without a tournament', async () => {
    try {
      // Create a match without a tournament
      const match = await matchesService.create({
        name: 'Test Match Without Tournament',
        isActive: true,
        description: 'A test match without a tournament',
        styleDivisions: JSON.stringify(['recurve', 'compound']),
        ageDivisions: JSON.stringify(['senior', 'junior']),
        forMen: true,
        forWomen: true,
        maxPlayersAmount: 10,
        organizerId: organizer.id
        // tournamentId is intentionally omitted
      }, userParams)

      assert.ok(match, 'Created a match without a tournament')
      assert.strictEqual(match.tournamentId, null, 'Match has no tournament ID')

      // Clean up the match
      await matchesService.remove(match.id, userParams)
    } catch (error) {
      assert.fail(`Should allow creation without a tournament: ${error}`)
    }
  })

  it('validates tournament when updating a match', async () => {
    // Create a match without a tournament
    const match = await matchesService.create({
      name: 'Test Match for Update',
      isActive: true,
      description: 'A test match for testing updates',
      styleDivisions: JSON.stringify(['recurve', 'compound']),
      ageDivisions: JSON.stringify(['senior', 'junior']),
      forMen: true,
      forWomen: true,
      maxPlayersAmount: 10,
      organizerId: organizer.id
    }, userParams)

    try {
      // Try to update with a non-existent tournament
      await matchesService.patch(match.id, {
        tournamentId: 99999 // Non-existent tournament ID
      }, userParams)
      assert.fail('Should not allow update with a non-existent tournament')
    } catch (error: any) {
      assert.ok(error instanceof BadRequest, 'Throws BadRequest for non-existent tournament')
      assert.ok(error.message.includes('tournament does not exist'), 'Error message mentions tournament existence')
    }

    try {
      // Update with a valid tournament
      const updatedMatch = await matchesService.patch(match.id, {
        tournamentId: tournament.id // Valid tournament ID
      }, userParams)

      assert.ok(updatedMatch, 'Updated a match with a valid tournament')
      assert.strictEqual(updatedMatch.tournamentId, tournament.id, 'Match has the correct tournament ID')

      // Clean up the match
      await matchesService.remove(match.id, userParams)
    } catch (error) {
      // Clean up the match even if the test fails
      await matchesService.remove(match.id, userParams)
      assert.fail(`Should allow update with a valid tournament: ${error}`)
    }
  })
})
