import type { Knex } from 'knex'

// Rename equipmentCategories -> styleDivisions, ageCategories -> ageDivisions in matches table
export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable('matches', table => {
        table.renameColumn('equipmentCategories', 'styleDivisions')
        table.renameColumn('ageCategories', 'ageDivisions')
    })
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable('matches', table => {
        table.renameColumn('styleDivisions', 'equipmentCategories')
        table.renameColumn('ageDivisions', 'ageCategories')
    })
}
