<script setup lang="ts">
import type { Tournament } from '@/api/feathers-client'
import { cn } from '@/lib/utils'
import { Calendar, MapPin, Users, Trophy } from 'lucide-vue-next'
import { computed } from 'vue'
import { useRouter } from 'vue-router'

const props = defineProps<{
  tournament: Tournament
  isSelected?: boolean
}>()

const emit = defineEmits<{
  select: [id: number]
  hover: [id: number | null]
}>()

const router = useRouter()

const handleClick = () => {
  emit('select', props.tournament.id)
}

const handleMouseEnter = () => {
  emit('hover', props.tournament.id)
}

const handleMouseLeave = () => {
  emit('hover', null)
}

const goToTournament = () => {
  router.push({ name: 'tournament-details', params: { id: props.tournament.id.toString() } })
}

const formatDate = (dateString?: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('pl-PL', { year: 'numeric', month: 'short', day: 'numeric' })
}

// Generate dots for tournament rounds
const roundDots = computed(() => {
  const totalRounds = props.tournament.totalRounds || 0
  const minRounds = props.tournament.minRounds || 0
  const dots = []
  
  for (let i = 1; i <= totalRounds; i++) {
    dots.push({
      round: i,
      isRequired: i <= minRounds,
      isCompleted: false // This would need to be determined from actual match data
    })
  }
  
  return dots
})

const organizerName = computed(() => {
  return props.tournament.organizer?.name || `Organizer ${props.tournament.organizerId}`
})

const federationName = computed(() => {
  return props.tournament.federation?.name || 'N/A'
})
</script>

<template>
  <div
    :class="cn(
      'flex flex-col gap-2 rounded-lg border p-3 text-left text-sm transition-all',
      (props.tournament as any).read ? 'bg-card' : 'bg-primary/5',
      (props.tournament as any).isHighlighted && 'border-l-4 border-l-primary',
      props.isSelected
        ? 'bg-blue-500/10'
        : 'hover:bg-accent/20'
    )"
    role="button"
    tabindex="0"
    @click="handleClick"
    @keydown.enter="handleClick"
    @keydown.space="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- Tournament name and status -->
    <div class="flex items-start justify-between gap-2">
      <div class="flex-1 min-w-0">
        <h3 class="font-semibold text-base truncate cursor-pointer hover:text-primary" @click.stop="goToTournament">
          {{ tournament.name }}
        </h3>
        <div class="flex items-center gap-2 mt-1">
          <Trophy class="h-4 w-4 text-yellow-500" />
          <span class="text-xs text-muted-foreground">{{ organizerName }}</span>
        </div>
      </div>
      <div class="flex flex-col items-end gap-1">
        <span v-if="tournament.isActive" class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
          Active
        </span>
        <span v-else-if="tournament.completedAt" class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full">
          Completed
        </span>
      </div>
    </div>

    <!-- Tournament rounds dots -->
    <div v-if="roundDots.length > 0" class="flex items-center gap-1 mt-2">
      <span class="text-xs text-muted-foreground mr-2">Rounds:</span>
      <div class="flex gap-1">
        <div
          v-for="dot in roundDots"
          :key="dot.round"
          :class="cn(
            'w-3 h-3 rounded-full border-2',
            dot.isRequired 
              ? 'bg-blue-500 border-blue-500' 
              : 'bg-gray-200 border-gray-300',
            dot.isCompleted && 'bg-green-500 border-green-500'
          )"
          :title="`Round ${dot.round}${dot.isRequired ? ' (Required)' : ' (Optional)'}`"
        />
      </div>
    </div>

    <!-- Tournament details -->
    <div class="flex flex-col gap-2 mt-1 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
      <div class="flex items-center gap-4">
        <div class="flex items-center gap-1.5">
          <Calendar class="h-4 w-4 text-muted-foreground" />
          <span class="text-xs">{{ formatDate(tournament.createdAt) }}</span>
        </div>
        <div v-if="federationName !== 'N/A'" class="flex items-center gap-1.5">
          <MapPin class="h-4 w-4 text-muted-foreground" />
          <span class="text-xs">{{ federationName }}</span>
        </div>
      </div>
      <div class="flex items-center gap-4">
        <div v-if="tournament.totalRounds" class="flex items-center gap-1.5">
          <Users class="h-4 w-4 text-muted-foreground" />
          <span class="text-xs">{{ tournament.totalRounds }} rounds</span>
        </div>
      </div>
    </div>

    <!-- Tournament description preview -->
    <div v-if="tournament.description" class="mt-2">
      <p class="text-xs text-muted-foreground line-clamp-2">
        {{ tournament.description }}
      </p>
    </div>
  </div>
</template>
