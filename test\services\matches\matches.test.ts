// For more information about this file see https://dove.feathersjs.com/guides/cli/service.test.html
import assert from 'assert'

import { app } from '../../../src/app'
import { createTestOrganizer } from '../../fixtures/create-test-organizer'

describe('matches service', () => {
  const service = app.service('matches')
  const usersService = app.service('users')
  const authService = app.service('authentication')
  const organizersService = app.service('organizers')

  // Sample match data for testing - adjusted to match the actual schema
  const testMatch = {
    name: 'Test Match 2025',
    isActive: true,
    description: 'This is a test match',
    address: 'Test Location',
    startDate: '2025-05-01',
    endDate: '2025-05-01',
    maxPlayersAmount: 10,
    country: 'Test Country',
    city: 'Test City',
    styleDivisions: JSON.stringify(['recurve', 'compound']),
    ageDivisions: JSON.stringify(['senior', 'junior']),
    forWomen: true,
    forMen: true
  }

  let userId: number
  let matchId: number
  let organizerId: number
  let accessToken: string
  let userParams: any

  // Create a test user before running match tests
  before(async () => {
    const testUser = await usersService.create({
      email: `test-matches-${Date.now()}@example.com`,
      password: 'supersecret'
    })

    userId = testUser.id

    // Authenticate the user
    const authResult = await authService.create({
      strategy: 'local',
      email: testUser.email,
      password: 'supersecret'
    })

    accessToken = authResult.accessToken

    // Create params with authentication
    userParams = {
      provider: 'rest',
      authentication: {
        strategy: 'jwt',
        accessToken
      },
      user: testUser
    }

    // Create a test organizer
    const organizer = await createTestOrganizer(userId, userParams)
    organizerId = organizer.id
  })

  // Clean up test user and organizer after tests
  after(async () => {
    try {
      // Clean up in the correct order due to foreign key constraints
      if (organizerId) {
        await organizersService.remove(organizerId, userParams)
      }
      await usersService.remove(userId)
    } catch (error) {
      console.error('Error cleaning up test data:', error)
    }
  })

  it('registered the service', () => {
    assert.ok(service, 'Registered the service')
  })

  it('requires authentication', async () => {
    try {
      await service.find()
      assert.fail('Should not allow unauthenticated access')
    } catch (error: any) {
      assert.ok(error, 'Returns an error for unauthenticated access')
    }
  })

  it('creates a match with user tracking fields', async () => {
    const match = await service.create({
      ...testMatch,
      organizerId
    }, userParams)

    assert.ok(match, 'Created a new match object')
    assert.ok(match.id, 'Match has an id')
    assert.equal(match.name, testMatch.name, 'Sets the name')
    assert.equal(match.description, testMatch.description, 'Sets the description')
    assert.equal(match.isActive, testMatch.isActive, 'Sets isActive status')

    // Verify user tracking fields
    assert.equal(match.createdBy, userId, 'Sets createdBy to current user ID')
    assert.equal(match.updatedBy, userId, 'Sets updatedBy to current user ID')
    assert.ok(match.createdAt, 'Sets createdAt timestamp')
    assert.ok(match.updatedAt, 'Sets updatedAt timestamp')

    // Save the ID for later tests
    matchId = match.id
  })

  it('gets a match', async () => {
    const match = await service.get(matchId, userParams)

    assert.ok(match, 'Got the match')
    assert.equal(match.id, matchId, 'Got the correct match')
    assert.equal(match.name, testMatch.name, 'Retrieved name matches')
  })

  it('lists matches with pagination', async () => {
    const matches = await service.find({
      ...userParams,
      query: {
        $limit: 10
      }
    })

    assert.ok(matches.data, 'Returns data array')
    assert.ok(matches.total >= 1, 'Returns at least one match')
    assert.ok(matches.limit === 10, 'Returns specified limit')
  })

  it('updates a match and tracks the user who made the update', async () => {
    const updatedData = {
      name: 'Updated Test Match'
    }

    const updated = await service.patch(matchId, updatedData, userParams)

    assert.equal(updated.name, updatedData.name, 'Updated the name')
    assert.equal(updated.id, matchId, 'ID remained the same')
    assert.equal(updated.description, testMatch.description, 'Unchanged fields remain the same')

    // Verify user tracking fields for update
    assert.equal(updated.updatedBy, userId, 'Sets updatedBy to current user ID')
    assert.ok(updated.updatedAt, 'Updates the updatedAt timestamp')
  })

  it('handles invalid data gracefully', async () => {
    try {
      // Create with invalid data (missing required fields)
      await service.create({ name: '', isActive: true }, userParams)
      assert.fail('Should have thrown an error for invalid data')
    } catch (error) {
      assert.ok(error, 'Error thrown for invalid data')
    }
  })

  it('removes a match and tracks who deleted it', async () => {
    const removed = await service.remove(matchId, userParams)

    assert.ok(removed, 'Removed the match')
    assert.equal(removed.id, matchId, 'Removed the correct match')

    // Verify user tracking fields for deletion
    assert.equal(removed.deletedBy, userId, 'Sets deletedBy to current user ID')
    assert.ok(removed.deletedAt, 'Sets deletedAt timestamp')

    try {
      await service.get(matchId, userParams)
      assert.fail('Should have thrown an error for deleted match')
    } catch (error) {
      assert.ok(error, 'Error thrown when getting deleted match')
    }
  })
})
