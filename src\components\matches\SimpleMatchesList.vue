<script setup lang="ts">
import type { Match } from '@/api/feathers-client'
import { useRouter } from 'vue-router'
import { Badge } from '@/components/ui/badge'

defineProps<{
  items: Match[]
}>()

const router = useRouter()

const goToMatch = (id: number) => {
  router.push({ name: 'match-details', params: { id: id.toString() } })
}

const displayDate = (date?: string) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleDateString('pl-PL', { year: 'numeric', month: 'short', day: 'numeric' })
}
</script>

<template>
  <div class="flex flex-col gap-1">
    <div v-if="!items || items.length === 0" class="p-4 text-center text-muted-foreground text-sm">
      No matches found
    </div>
    <div v-else>
      <button
        v-for="item in items"
        :key="item.id"
        @click="goToMatch(item.id)"
        class="w-full flex items-center justify-between px-3 py-2 rounded hover:bg-accent transition text-left text-sm border-b last:border-b-0"
        style="min-height: 0"
      >
        <div class="flex flex-col gap-0.5">
          <span class="font-medium">{{ item.name }}</span>
          <span class="text-xs text-muted-foreground">
            {{ displayDate(item.startDate) }}
            <span v-if="item.city">· {{ item.city }}</span>
            <span v-if="item.country">· {{ item.country }}</span>
          </span>
        </div>
        <Badge v-if="item.competitionLevel" variant="secondary" class="ml-2 text-xs">
          {{ item.competitionLevel }}
        </Badge>
      </button>
    </div>
  </div>
</template>
