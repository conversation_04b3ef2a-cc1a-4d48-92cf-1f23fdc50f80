// For more information about this file see https://dove.feathersjs.com/guides/cli/service.schemas.html
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, querySyntax } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'

import type { HookContext } from '../../declarations'
import { dataValidator, queryValidator } from '../../validators'
import { matchSchema } from '../matches/matches.schema'
import { playerSchema } from '../players/players.schema'
import type { MatchRegistrationsService } from './match-registrations.class'

export enum MatchRegistrationStatus {
  Imported = 'imported', // Imported from legacy or external system
  Draft = 'draft', // Registration started but not submitted
  Submitted = 'submitted', // Registration submitted by user
  Pending = 'pending', // Awaiting review or approval
  Reviewing = 'reviewing', // Under review by organizer
  Info = 'info', // Awaiting additional information
  Ineligible = 'ineligible', // Not eligible to participate
  Approved = 'approved', // Registration approved
  Payment = 'payment', // Awaiting payment
  Paid = 'paid', // Payment received
  Failed = 'failed', // Registration or payment failed
  Withdrawn = 'withdrawn', // Registration withdrawn by user
  Assigned = 'assigned', // Participant assigned to group/lane
  Ready = 'ready', // Ready to participate
  Active = 'active', // Currently participating
  Completed = 'completed', // Participation completed
  Noshow = 'noshow', // Did not show up
  Disqualified = 'disqualified', // Disqualified from event
  Rejected = 'rejected', // Registration rejected
  Suspended = 'suspended', // Temporarily suspended
  Expired = 'expired' // Registration expired
}

export const matchRegistrationSchema = Type.Object(
  {
    id: Type.Number(),
    matchId: Type.Number(),
    playerId: Type.Number(),
    registrationDate: Type.String({ format: 'date-time' }),
    status: Type.Enum(MatchRegistrationStatus),
    styleDivision: Type.Optional(Type.String()),
    ageDivision: Type.Optional(Type.String()),
    genderDivision: Type.Optional(Type.String()),
    registrationDetails: Type.Optional(Type.Unknown()),
    equipmentId: Type.Optional(Type.Number()),
    createdAt: Type.String({ format: 'date-time' }),
    updatedAt: Type.String({ format: 'date-time' }),
    deletedAt: Type.Optional(Type.String({ format: 'date-time' })),
    createdBy: Type.Optional(Type.Number()),
    updatedBy: Type.Optional(Type.Number()),
    deletedBy: Type.Optional(Type.Number()),
    // Virtual fields for related data
    match: Type.Optional(Type.Ref(matchSchema)),
    player: Type.Optional(Type.Ref(playerSchema)),
    isPaid: Type.Optional(Type.Boolean()),
    isConfirmed: Type.Optional(Type.Boolean())
  },
  { $id: 'MatchRegistration', additionalProperties: false }
)
export type MatchRegistration = Static<typeof matchRegistrationSchema>
export const matchRegistrationValidator = getValidator(matchRegistrationSchema, dataValidator)
export const matchRegistrationResolver = resolve<MatchRegistration, HookContext<MatchRegistrationsService>>({})

export const matchRegistrationExternalResolver = resolve<MatchRegistration, HookContext<MatchRegistrationsService>>({})

// Schema for creating new entries
export const matchRegistrationDataSchema = Type.Intersect([
  Type.Pick(matchRegistrationSchema, [
    'matchId', 'playerId', 'styleDivision', 'ageDivision', 'genderDivision'
  ]),
  Type.Partial(
    Type.Pick(matchRegistrationSchema, [
      'registrationDetails', 'equipmentId', 'isPaid', 'isConfirmed', 'status', 'registrationDate', 'createdAt'
    ])
  )
], {
  $id: 'MatchRegistrationData'
})
export type MatchRegistrationData = Static<typeof matchRegistrationDataSchema>
export const matchRegistrationDataValidator = getValidator(matchRegistrationDataSchema, dataValidator)
export const matchRegistrationDataResolver = resolve<MatchRegistration, HookContext<MatchRegistrationsService>>({
  registrationDate: async (value) => {
    return value ?? new Date().toISOString();
  },
  status: async (value) => {
    return value ?? MatchRegistrationStatus.Submitted;
  },
  createdAt: async (value) => {
    return value ?? new Date().toISOString();
  },
  updatedAt: async (value) => {
    return value ?? new Date().toISOString();
  }
})

// Schema for updating existing entries
export const matchRegistrationPatchSchema = Type.Partial(
  Type.Pick(matchRegistrationSchema, ['status', 'styleDivision', 'ageDivision', 'genderDivision', 'registrationDetails', 'equipmentId', 'deletedBy', 'deletedAt']),
  {
    $id: 'MatchRegistrationPatch'
  }
)
export type MatchRegistrationPatch = Static<typeof matchRegistrationPatchSchema>
export const matchRegistrationPatchValidator = getValidator(matchRegistrationPatchSchema, dataValidator)
export const matchRegistrationPatchResolver = resolve<MatchRegistration, HookContext<MatchRegistrationsService>>({
  updatedAt: async () => {
    return new Date().toISOString()
  }
})

// Schema for allowed query properties
export const matchRegistrationQueryProperties = Type.Pick(matchRegistrationSchema, [
  'id',
  'matchId',
  'playerId',
  'status',
  'styleDivision',
  'ageDivision',
  'genderDivision',
  'equipmentId',
  'registrationDate'
])
export const matchRegistrationQuerySchema = Type.Intersect(
  [
    querySyntax(matchRegistrationQueryProperties),
    // Add additional query properties here
    Type.Object({
      $populate: Type.Optional(Type.Union([Type.String(), Type.Array(Type.String())]))
    }, { additionalProperties: false })
  ],
  { additionalProperties: false }
)
export type MatchRegistrationQuery = Static<typeof matchRegistrationQuerySchema>
export const matchRegistrationQueryValidator = getValidator(matchRegistrationQuerySchema, queryValidator)
export const matchRegistrationQueryResolver = resolve<MatchRegistrationQuery, HookContext<MatchRegistrationsService>>({})
