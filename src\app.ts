import swagger from 'feathers-swagger'

import configuration from '@feathersjs/configuration'
// For more information about this file see https://dove.feathersjs.com/guides/cli/application.html
import { feathers } from '@feathersjs/feathers'
import { bodyParser, cors, errorHandler, koa, parseAuthentication, rest, serveStatic } from '@feathersjs/koa'
import socketio from '@feathersjs/socketio'
import { Type } from '@feathersjs/typebox'; // Added for defining global schemas

import { authentication } from './authentication'
import { channels } from './channels'
import { configurationValidator } from './configuration'
import type { Application } from './declarations'
import { importMode } from './hooks/importMode'
import { logError } from './hooks/log-error'
import { postgresql } from './postgresql'
import { services } from './services/index'

const app: Application = koa(feathers())

// Load our app configuration (see config/ folder)
app.configure(configuration(configurationValidator))

// Set up Koa middleware
app.use(cors())
app.use(serveStatic(app.get('public')))
app.use(errorHandler())
app.use(parseAuthentication())
app.use(bodyParser())

// Configure services and transports
// Conditionally enable Swagger documentation based on NODE_ENV
if (process.env.NODE_ENV !== 'production') {
  app.configure(swagger({
    specs: {
      info: {
        title: 'Archery Points API',
        description: 'API for Archery Points',
        version: '0.1.6',
      },
      components: { // Added components section
        schemas: {
          // Changed key and $id from 'provider' to ':provider'
          ':provider': Type.String({
            $id: ':provider', // Schema ID now includes the colon
            type: 'string',
            description: 'The name of the OAuth provider (e.g., "github", "google", "facebook").',
            example: 'github'
          }),
          // Added schema for :providerList to resolve the error
          ':providerList': Type.Object({}, {
            $id: ':providerList',
            additionalProperties: true, // Allows any properties
            description: 'Response for OAuth provider interactions. Note: GET /oauth/:provider typically results in a redirect.'
          })
          // You can add other global schemas here if needed
        }
      }
    },
    ui: swagger.swaggerUI({}),
  }))
}

app.configure(rest())
app.configure(
  socketio({
    cors: {
      origin: app.get('origins')
    }
  })
)
app.configure(postgresql)
app.configure(authentication)
app.configure(services)
app.configure(channels)

app.hooks({
  around: [logError],
  before: [importMode],
  after: [],
  error: []
})

app.hooks({
  setup: [],
  teardown: []
})

export { app }
