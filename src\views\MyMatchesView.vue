<script setup lang="ts">

import { computed, onMounted, watch, ref } from 'vue'
import { useMatchesService } from '@/stores/matches'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import SimpleMatchesList from '@/components/matches/SimpleMatchesList.vue'
import MatchDetailsWidget from '@/components/matches/MatchDetailsWidget.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  SidebarInset,
  SidebarProvider,
  Sidebar,
  SidebarContent,
} from '@/components/ui/sidebar'
import { Calendar, Users, Trophy, AlertCircle } from 'lucide-vue-next'
import type { Match, MatchRegistration } from '@/api/feathers-client'

import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const matchesService = useMatchesService()
const userStore = useUserStore()
const { activePlayer } = storeToRefs(userStore)

// Local state management
const matchRegistrations = ref<MatchRegistration[]>([])
const currentMatch = ref<Match | null>(null)
const isLoadingRegistrations = ref(false)
const error = ref<Error | null>(null)

const isLoading = computed(() => isLoadingRegistrations.value)

// Function to fetch match registrations for the active player
const fetchMatchRegistrations = async () => {
  if (!activePlayer.value) return

  isLoadingRegistrations.value = true
  error.value = null
  try {
    matchRegistrations.value = await matchesService.findMatchRegistrations({
      query: {
        playerId: activePlayer.value.id,
        $limit: 100,
        $populate: 'match'
      }
    })
  } catch (err) {
    error.value = err instanceof Error ? err : new Error('Failed to load match registrations')
    console.error('Error loading match registrations:', err)
  } finally {
    isLoadingRegistrations.value = false
  }
}

// Get matches where the user is registered (from populated registrations)
const myMatches = computed(() => {
  if (!activePlayer.value || !matchRegistrations.value.length) {
    return []
  }
  // Only use registrations for this player, and only those with a populated match
  return matchRegistrations.value
    .filter(reg => reg.playerId === activePlayer.value!.id && reg.match)
    .map(reg => reg.match!)
    .filter(match => match !== undefined) as Match[]
})

// Group matches by status
const upcomingMatches = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  return myMatches.value.filter(match => {
    if (!match.startDate) return false
    const startDate = new Date(match.startDate)
    return startDate >= today
  })
})

const pastMatches = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  return myMatches.value.filter(match => {
    if (!match.startDate) return false
    const startDate = new Date(match.startDate)
    return startDate < today
  })
})

// Watch for changes in activePlayer and fetch registrations when it becomes available
watch(activePlayer, (newActivePlayer: typeof activePlayer.value) => {
  if (newActivePlayer) {
    fetchMatchRegistrations()
  }
}, { immediate: true })

onMounted(async () => {
  // Fetch match registrations if we have an active player
  if (activePlayer.value) {
    await fetchMatchRegistrations()
  }
})
</script>

<template>
  <SidebarProvider>
    <SidebarInset>
      <div class="min-h-screen bg-background">
        <div class="container mx-auto px-4 py-6">
          <!-- Header -->
          <div class="mb-6">
            <h1 class="text-3xl font-bold mb-2">{{ t('views.myMatches.title') }}</h1>
            <p class="text-muted-foreground">{{ t('views.myMatches.description') }}</p>
          </div>

          <!-- Loading State -->
          <div v-if="isLoading" class="flex items-center justify-center py-12">
            <div class="text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p class="text-muted-foreground">{{ t('views.myMatches.loadingMatches') }}</p>
            </div>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="flex items-center justify-center py-12">
            <Card class="w-full max-w-md">
              <CardContent class="pt-6">
                <div class="text-center">
                  <AlertCircle class="h-8 w-8 text-red-500 mx-auto mb-4" />
                  <p class="text-red-500 mb-2">{{ t('views.myMatches.errorLoadingMatches') }}</p>
                  <p class="text-muted-foreground text-sm">{{ error.message }}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- No Matches State -->
          <div v-else-if="myMatches.length === 0" class="flex items-center justify-center py-12">
            <Card class="w-full max-w-md">
              <CardContent class="pt-6">
                <div class="text-center">
                  <Trophy class="h-8 w-8 text-muted-foreground mx-auto mb-4" />
                  <h3 class="text-lg font-semibold mb-2">{{ t('views.myMatches.noMatchesYet') }}</h3>
                  <p class="text-muted-foreground mb-4">{{ t('views.myMatches.noMatchesDescription') }}</p>
                  <Button>
                    <router-link to="/matches/search">
                      {{ t('views.myMatches.browseMatches') }}
                    </router-link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Matches Content -->
          <div v-else class="space-y-8">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent class="pt-6">
                  <div class="flex items-center">
                    <Calendar class="h-4 w-4 text-muted-foreground" />
                    <div class="ml-2">
                      <p class="text-2xl font-bold">{{ upcomingMatches.length }}</p>
                      <p class="text-xs text-muted-foreground">{{ t('common.upcoming') }}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent class="pt-6">
                  <div class="flex items-center">
                    <Trophy class="h-4 w-4 text-muted-foreground" />
                    <div class="ml-2">
                      <p class="text-2xl font-bold">{{ pastMatches.length }}</p>
                      <p class="text-xs text-muted-foreground">{{ t('common.completed') }}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent class="pt-6">
                  <div class="flex items-center">
                    <Users class="h-4 w-4 text-muted-foreground" />
                    <div class="ml-2">
                      <p class="text-2xl font-bold">{{ myMatches.length }}</p>
                      <p class="text-xs text-muted-foreground">{{ t('common.total') }}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Upcoming Matches -->
            <div v-if="upcomingMatches.length > 0">
              <div class="flex items-center gap-4 mb-4">
                <h2 class="text-2xl font-semibold">{{ t('views.myMatches.upcomingMatches') }}</h2>
                <Badge variant="secondary">{{ upcomingMatches.length }}</Badge>
              </div>
              <SimpleMatchesList :items="upcomingMatches" />
            </div>

            <Separator v-if="upcomingMatches.length > 0 && pastMatches.length > 0" />

            <!-- Past Matches -->
            <div v-if="pastMatches.length > 0">
              <div class="flex items-center gap-4 mb-4">
                <h2 class="text-2xl font-semibold">{{ t('views.myMatches.pastMatches') }}</h2>
                <Badge variant="outline">{{ pastMatches.length }}</Badge>
              </div>
              <SimpleMatchesList :items="pastMatches" />
            </div>
          </div>
        </div>
      </div>
    </SidebarInset>

    <!-- Match Details Sidebar -->
    <Sidebar
      class="sticky top-0 h-svh border-l"
      collapsible="offcanvas"
    >
      <SidebarContent>
        <MatchDetailsWidget :match="currentMatch" />
      </SidebarContent>
    </Sidebar>
  </SidebarProvider>
</template>
