import axios from 'axios';
import { addDays, format, subDays, subYears } from 'date-fns';

const FORECAST_API_URL = 'https://api.open-meteo.com/v1/forecast';
const ARCHIVE_API_URL = 'https://archive-api.open-meteo.com/v1/archive';

interface WeatherParams {
  latitude: number;
  longitude: number;
  date: Date;
}

interface DailyWeatherParams {
  latitude: number;
  longitude: number;
  timezone: string;
  start_date?: string;
  end_date?: string;
  daily?: string;
  past_days?: number;
  forecast_days?: number;
}

// Define a basic structure for the expected API response
interface WeatherData {
  daily?: {
    time: string[];
    weather_code?: number[];
    temperature_2m_max?: number[];
    temperature_2m_min?: number[];
    sunrise?: string[]; // From forecast API
    sunset?: string[];  // From forecast API
    // precipitation_sum is no longer explicitly requested as per new examples
  };
  // Add other fields as needed based on API response, like daily_units, timezone etc.
  daily_units?: Record<string, string>;
  timezone?: string;
  timezone_abbreviation?: string;
  utc_offset_seconds?: number;
  latitude?: number;
  longitude?: number;
  generationtime_ms?: number;
  elevation?: number;
}

export async function getWeatherForecast({ latitude, longitude, date }: WeatherParams): Promise<WeatherData> {
  const today = new Date();
  // Clear time component for accurate date comparisons
  today.setHours(0, 0, 0, 0);
  const targetDate = new Date(date);
  targetDate.setHours(0, 0, 0, 0);


  const fiveDaysAgo = subDays(today, 5);
  const sixteenDaysAhead = addDays(today, 16); // Forecast API provides up to 16 days

  let apiUrl: string;
  const params: DailyWeatherParams = {
    latitude,
    longitude,
    timezone: 'auto',
  };

  if (targetDate < fiveDaysAgo) {
    // More than 5 days in the past: use historical data for that specific date
    apiUrl = ARCHIVE_API_URL;
    const dateStr = format(targetDate, 'yyyy-MM-dd');
    params.start_date = dateStr;
    params.end_date = dateStr;
    params.daily = 'weather_code,temperature_2m_max,temperature_2m_min';
  } else if (targetDate >= fiveDaysAgo && targetDate <= sixteenDaysAhead) {
    // From 5 days ago up to 16 days ahead: use forecast API
    apiUrl = FORECAST_API_URL;

    // Calculate how many days of forecast are needed *from today*
    // If targetDate is in the past (but within 5 days ago), we still need to fetch it.
    // The forecast API's `forecast_days` is relative to the current day.
    // If targetDate is today, daysFromToday is 0. We need forecast_days = 1.
    // If targetDate is tomorrow, daysFromToday is 1. We need forecast_days = 2.
    // So, forecast_days should be daysFromToday + 1.
    // The API also supports `past_days` if we want to get data from before today using the forecast endpoint.
    // Given the logic: "for dates from 5 days ago do 16 days ahead use forecast api"
    // We can use `past_days` for dates between 5 days ago and yesterday.
    // And `forecast_days` for today up to 16 days ahead.

    const daysDifference = Math.ceil((targetDate.getTime() - today.getTime()) / (1000 * 3600 * 24));

    if (daysDifference < 0) { // Target date is in the past (up to 5 days)
      params.daily = 'weather_code,temperature_2m_max,temperature_2m_min,sunrise,sunset';
      params.past_days = Math.min(Math.abs(daysDifference), 5); // API max past_days is often 5 or 7
    } else { // Target date is today or in the future (up to 16 days)
      const forecastDaysToRequest = Math.ceil((targetDate.getTime() - today.getTime()) / (1000 * 3600 * 24)) + 1;
      params.daily = 'weather_code,temperature_2m_max,temperature_2m_min,sunrise,sunset';
      params.forecast_days = Math.min(forecastDaysToRequest, 16);
    }
  } else {
    // More than 16 days ahead: use historical data from last year for that specific date
    apiUrl = ARCHIVE_API_URL;
    const lastYearDate = subYears(targetDate, 1);
    const dateStr = format(lastYearDate, 'yyyy-MM-dd');
    params.start_date = dateStr;
    params.end_date = dateStr;
    params.daily = 'weather_code,temperature_2m_max,temperature_2m_min';
  }

  try {
    // console.log('Fetching weather data from:', apiUrl, 'with params:', params);
    const response = await axios.get<WeatherData>(apiUrl, { params });
    // console.log('Weather data response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching weather data:', error);
    throw error; // Rethrow or handle as appropriate for your application
  }
}
