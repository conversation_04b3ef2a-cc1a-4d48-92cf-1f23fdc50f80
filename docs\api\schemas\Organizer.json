{"name": "Organizer", "schema": {"required": ["id", "userId", "name"], "type": "object", "properties": {"id": {"type": "number"}, "userId": {"type": "number"}, "name": {"type": "string"}, "bankAccount": {"type": "string"}, "taxId": {"type": "string"}, "contactPerson": {"type": "string"}, "about": {"type": "string"}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "number"}, "updatedBy": {"type": "number"}, "deletedBy": {"type": "number"}, "user": {"$ref": "#/components/schemas/User"}, "legacyId": {"type": "number"}, "address": {"type": "string"}, "zipcode": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "phone": {"type": "string"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "avatar": {"type": "string"}}, "additionalProperties": false}}