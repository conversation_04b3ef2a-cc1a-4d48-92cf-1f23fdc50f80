// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#database-services
import type { Params } from '@feathersjs/feathers'
import { KnexService } from '@feathersjs/knex'
import type { KnexAdapterOptions, KnexAdapterParams } from '@feathersjs/knex'

import type { Application } from '../../declarations'
import type { MatchRegistration, MatchRegistrationData, MatchRegistrationPatch, MatchRegistrationQuery } from './match-registrations.schema'

export type { MatchRegistration, MatchRegistrationData, MatchRegistrationPatch, MatchRegistrationQuery }

export interface MatchRegistrationParams extends KnexAdapterParams<MatchRegistrationQuery> { }

// By default calls the standard Knex adapter service methods but can be customized with your own functionality.
export class MatchRegistrationsService<ServiceParams extends Params = MatchRegistrationParams> extends KnexService<
  MatchRegistration,
  MatchRegistrationData,
  MatchRegistrationParams,
  MatchRegistrationPatch
> { }

export const getOptions = (app: Application): KnexAdapterOptions => {
  return {
    paginate: app.get('paginate'),
    Model: app.get('postgresqlClient'),
    name: 'match_registrations',
    whitelist: ['$populate']
  }
}
