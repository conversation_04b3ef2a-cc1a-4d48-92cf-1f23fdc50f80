import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { useUserStore } from '../user'
import { useAuthStore } from '../auth'

// Mock the API
vi.mock('../../api/feathers-client', () => ({
  api: {
    userMe: {
      find: vi.fn(),
      patch: vi.fn()
    }
  }
}))

describe('User Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('should initialize with empty state', () => {
    const userStore = useUserStore()
    
    expect(userStore.userProfile).toBeNull()
    expect(userStore.isLoading).toBe(false)
    expect(userStore.error).toBeNull()
    expect(userStore.hasProfile).toBe(false)
    expect(userStore.fullName).toBe('')
  })

  it('should clear user profile', () => {
    const userStore = useUserStore()
    
    // Set some initial data
    userStore.userProfile = {
      id: 1,
      email: '<EMAIL>'
    }
    userStore.error = new Error('Test error')
    
    userStore.clearUserProfile()
    
    expect(userStore.userProfile).toBeNull()
    expect(userStore.error).toBeNull()
  })

  it('should compute fullName from email when no other name is available', () => {
    const userStore = useUserStore()
    
    userStore.userProfile = {
      id: 1,
      email: '<EMAIL>'
    }
    
    expect(userStore.fullName).toBe('<EMAIL>')
  })

  it('should indicate hasProfile when userProfile exists', () => {
    const userStore = useUserStore()
    
    expect(userStore.hasProfile).toBe(false)
    
    userStore.userProfile = {
      id: 1,
      email: '<EMAIL>'
    }
    
    expect(userStore.hasProfile).toBe(true)
  })
})
