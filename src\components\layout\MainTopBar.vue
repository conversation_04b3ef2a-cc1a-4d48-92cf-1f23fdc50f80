<script setup lang="ts">
import { Separator } from '@/components/ui/separator'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { Bell, Settings, Trophy, Medal, Target, Route, Crosshair } from 'lucide-vue-next'
import { ref } from 'vue'
import { <PERSON><PERSON> } from '@/components/ui/button'
import LanguageSelector from '@/components/common/LanguageSelector.vue'

// Medal counts (these would normally come from a store)
const goldMedals = ref(12)
const silverMedals = ref(12)
const bronzeMedals = ref(12)

// Stats (these would normally come from a store)
const matches = ref(12)
const totalHours = ref('15,32')
const accuracy = ref('63,74%')
const distance = ref('2 450 km')
</script>

<template>
  <header class="sticky top-0 z-50 flex h-14 shrink-0 items-center border-b bg-background px-3">
    <div class="flex flex-1 items-center justify-between">
      <div class="flex items-center gap-2">
        <SidebarTrigger />

        <!-- Stats Group 1: Medals -->
        <div class="flex items-center gap-1 bg-gray-50 rounded-lg px-2 py-1 border border-gray-200">
          <div class="flex items-center gap-1">
            <Trophy class="h-4 w-4 text-yellow-500" />
            <span class="text-xs font-medium">{{ goldMedals }}</span>
          </div>
          <Separator orientation="vertical" class="h-4 mx-0.5" />
          <div class="flex items-center gap-1">
            <Trophy class="h-4 w-4 text-gray-400" />
            <span class="text-xs font-medium">{{ silverMedals }}</span>
          </div>
          <Separator orientation="vertical" class="h-4 mx-0.5" />
          <div class="flex items-center gap-1">
            <Trophy class="h-4 w-4 text-amber-600" />
            <span class="text-xs font-medium">{{ bronzeMedals }}</span>
          </div>
        </div>

        <!-- Stats Group 2: Medals by Category -->
        <div class="flex items-center gap-1 bg-gray-50 rounded-lg px-2 py-1 border border-gray-200">
          <div class="flex items-center gap-1">
            <Medal class="h-4 w-4 text-yellow-500" />
            <span class="text-xs font-medium">{{ goldMedals }}</span>
          </div>
          <Separator orientation="vertical" class="h-4 mx-0.5" />
          <div class="flex items-center gap-1">
            <Medal class="h-4 w-4 text-gray-400" />
            <span class="text-xs font-medium">{{ silverMedals }}</span>
          </div>
          <Separator orientation="vertical" class="h-4 mx-0.5" />
          <div class="flex items-center gap-1">
            <Medal class="h-4 w-4 text-amber-600" />
            <span class="text-xs font-medium">{{ bronzeMedals }}</span>
          </div>
        </div>

        <!-- Stats Group 3: Competition Stats -->
        <div class="flex items-center gap-2 bg-gray-50 rounded-lg px-2 py-1 border border-gray-200">
          <div class="flex items-center gap-1">
            <Target class="h-4 w-4 text-blue-500" />
            <span class="text-xs font-medium">{{ matches }}</span>
          </div>
          <Separator orientation="vertical" class="h-4" />
          <div class="flex items-center gap-1">
            <Trophy class="h-4 w-4 text-blue-500" />
            <span class="text-xs font-medium">{{ totalHours }}</span>
          </div>
          <Separator orientation="vertical" class="h-4" />
          <div class="flex items-center gap-1">
            <Crosshair class="h-4 w-4 text-blue-500" />
            <span class="text-xs font-medium">{{ accuracy }}</span>
          </div>
          <Separator orientation="vertical" class="h-4" />
          <div class="flex items-center gap-1">
            <Route class="h-4 w-4 text-blue-500" />
            <span class="text-xs font-medium">{{ distance }}</span>
          </div>
        </div>
      </div>

      <!-- Right side controls -->
      <div class="flex items-center space-x-2">
        <!-- Language Selector -->
        <LanguageSelector />
        <Button variant="ghost" size="icon">
          <Bell class="h-5 w-5" />
        </Button>

        <Button variant="ghost" size="icon">
          <Settings class="h-5 w-5" />
        </Button>


      </div>
    </div>
  </header>
</template>
