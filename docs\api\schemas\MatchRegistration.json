{"name": "MatchRegistration", "schema": {"required": ["id", "matchId", "playerId", "registrationDate", "status", "createdAt", "updatedAt"], "type": "object", "properties": {"id": {"type": "number"}, "matchId": {"type": "number"}, "playerId": {"type": "number"}, "registrationDate": {"type": "string", "format": "date-time"}, "status": {"anyOf": [{"type": "string", "const": "imported"}, {"type": "string", "const": "draft"}, {"type": "string", "const": "submitted"}, {"type": "string", "const": "pending"}, {"type": "string", "const": "reviewing"}, {"type": "string", "const": "info"}, {"type": "string", "const": "ineligible"}, {"type": "string", "const": "approved"}, {"type": "string", "const": "payment"}, {"type": "string", "const": "paid"}, {"type": "string", "const": "failed"}, {"type": "string", "const": "withdrawn"}, {"type": "string", "const": "assigned"}, {"type": "string", "const": "ready"}, {"type": "string", "const": "active"}, {"type": "string", "const": "completed"}, {"type": "string", "const": "noshow"}, {"type": "string", "const": "disqualified"}, {"type": "string", "const": "rejected"}, {"type": "string", "const": "suspended"}, {"type": "string", "const": "expired"}]}, "styleDivision": {"type": "string"}, "ageDivision": {"type": "string"}, "genderDivision": {"type": "string"}, "registrationDetails": {}, "equipmentId": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "number"}, "updatedBy": {"type": "number"}, "deletedBy": {"type": "number"}, "match": {"$ref": "#/components/schemas/Match"}, "player": {"$ref": "#/components/schemas/Player"}, "isPaid": {"type": "boolean"}, "isConfirmed": {"type": "boolean"}}, "additionalProperties": false}}