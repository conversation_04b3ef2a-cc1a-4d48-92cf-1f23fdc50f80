import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useEquipmentStore } from '../equipment'
import { useAuthStore } from '../auth'
import { useUserStore } from '../user'

// Mock the API
vi.mock('../../api/feathers-client', () => ({
  api: {
    equipment: {
      find: vi.fn(),
      create: vi.fn(),
      patch: vi.fn(),
      remove: vi.fn()
    }
  }
}))

describe('Equipment Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  it('should initialize with empty state', () => {
    const equipmentStore = useEquipmentStore()
    
    expect(equipmentStore.equipment).toEqual([])
    expect(equipmentStore.currentEquipment).toBeNull()
    expect(equipmentStore.isLoading).toBe(false)
    expect(equipmentStore.error).toBeNull()
    expect(equipmentStore.hasEquipment).toBe(false)
  })

  it('should select equipment', () => {
    const equipmentStore = useEquipmentStore()
    
    const mockEquipment = {
      id: 1,
      name: 'Test Bow',
      category: 'Recurve',
      isActive: true,
      isDefault: false
    }
    
    equipmentStore.selectEquipment(mockEquipment)
    
    expect(equipmentStore.currentEquipment).toEqual(mockEquipment)
  })

  it('should clear equipment', () => {
    const equipmentStore = useEquipmentStore()
    
    // Set some initial data
    equipmentStore.equipment = [
      { id: 1, name: 'Test Bow', category: 'Recurve', isActive: true, isDefault: false }
    ]
    equipmentStore.currentEquipment = equipmentStore.equipment[0]
    equipmentStore.error = new Error('Test error')
    
    equipmentStore.clearEquipment()
    
    expect(equipmentStore.equipment).toEqual([])
    expect(equipmentStore.currentEquipment).toBeNull()
    expect(equipmentStore.error).toBeNull()
  })

  it('should compute equipment by category', () => {
    const equipmentStore = useEquipmentStore()
    
    equipmentStore.equipment = [
      { id: 1, name: 'Recurve Bow', category: 'Recurve', isActive: true, isDefault: false },
      { id: 2, name: 'Compound Bow', category: 'Compound', isActive: true, isDefault: false },
      { id: 3, name: 'Another Recurve', category: 'Recurve', isActive: true, isDefault: false }
    ]
    
    const byCategory = equipmentStore.equipmentByCategory
    
    expect(byCategory.Recurve).toHaveLength(2)
    expect(byCategory.Compound).toHaveLength(1)
    expect(byCategory.Recurve[0].name).toBe('Recurve Bow')
    expect(byCategory.Compound[0].name).toBe('Compound Bow')
  })

  it('should identify default equipment', () => {
    const equipmentStore = useEquipmentStore()
    
    equipmentStore.equipment = [
      { id: 1, name: 'Regular Bow', category: 'Recurve', isActive: true, isDefault: false },
      { id: 2, name: 'Default Bow', category: 'Compound', isActive: true, isDefault: true }
    ]
    
    expect(equipmentStore.defaultEquipment?.name).toBe('Default Bow')
  })
})
