<script setup lang="ts">
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { refDebounced, useWindowSize } from '@vueuse/core'
import {
  Search,
  Filter,
} from 'lucide-vue-next'
import { computed, ref, onMounted } from 'vue'
import TournamentsList from './TournamentsList.vue'
import TournamentDetailsWidget from './TournamentDetailsWidget.vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

import ArcheryMap from '@/components/map/ArcheryMap.vue'
import {
  SidebarInset,
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarTrigger,
} from '@/components/ui/sidebar'
import type { Tournament, Match } from '@/api/feathers-client'
import { useTournamentsService } from '@/stores/tournaments'
import { useI18n } from 'vue-i18n'

// Type for tournament with populated matches
interface TournamentWithMatches extends Tournament {
  matches?: Match[]
}

interface TournamentsProps {
  defaultLayout?: number[]
  navCollapsedSize?: number
}

const props = withDefaults(defineProps<TournamentsProps>(), {
  defaultLayout: () => [265, 655],
  navCollapsedSize: 44
})

const { t } = useI18n()
const { width } = useWindowSize()
const tournamentsService = useTournamentsService()

// Local state management
const tournaments = ref<TournamentWithMatches[]>([])
const currentTournament = ref<TournamentWithMatches | null>(null)
const hoveredTournaments = ref<Tournament[]>([])
const hoveredTournamentId = ref<number | null>(null)
const isLoading = ref(false)
const error = ref<Error | null>(null)

// Matches state for the selected tournament
const tournamentMatches = ref<Match[]>([])
const isLoadingMatches = ref(false)

// Search and filter state
const searchQuery = ref('')
const debouncedSearchQuery = refDebounced(searchQuery, 300)

// Sidebar styles for responsive design
const sidebarStyles = computed(() => {
  if (width.value >= 1024) {
    return { '--sidebar-width': '25rem' }
  } else if (width.value >= 768) {
    return { '--sidebar-width': '15rem' }
  } else {
    return { '--sidebar-width': '25rem' }
  }
})

// Fetch tournaments
const fetchTournaments = async () => {
  isLoading.value = true
  error.value = null
  try {
    const result = await tournamentsService.findTournaments({
      query: {
        $limit: 100,
        isActive: true,
        $populate: 'matches'
      }
    })
    tournaments.value = result || []
  } catch (err) {
    error.value = err instanceof Error ? err : new Error('Failed to fetch tournaments')
    console.error('Failed to fetch tournaments:', err)
  } finally {
    isLoading.value = false
  }
}

// Filtered tournaments based on search
const filteredTournaments = computed(() => {
  if (!debouncedSearchQuery.value) {
    return tournaments.value
  }

  const query = debouncedSearchQuery.value.toLowerCase()
  return tournaments.value.filter(tournament =>
    tournament.name?.toLowerCase().includes(query) ||
    tournament.description?.toLowerCase().includes(query) ||
    tournament.organizer?.name?.toLowerCase().includes(query)
  )
})

// Active tournaments
const activeTournaments = computed(() =>
  filteredTournaments.value.filter(t => t.isActive)
)

// Completed tournaments
const completedTournaments = computed(() =>
  filteredTournaments.value.filter(t => t.completedAt)
)

// Tournament selection handlers
const selectTournament = (id: number) => {
  const tournament = tournaments.value.find(t => t.id === id)
  currentTournament.value = tournament || null

  // Get matches from the populated tournament data
  if (tournament?.matches) {
    tournamentMatches.value = tournament.matches.filter(match => match.isActive) || []
  } else {
    tournamentMatches.value = []
  }
}

const setHoveredTournamentId = (id: number | null) => {
  hoveredTournamentId.value = id
  if (id) {
    const tournament = tournaments.value.find(t => t.id === id)
    hoveredTournaments.value = tournament ? [tournament] : []
  } else {
    hoveredTournaments.value = []
  }
}

const handleSelectTournamentFromMap = (tournamentId: string) => {
  const id = parseInt(tournamentId, 10)
  selectTournament(id)
}

// Map coordinates - tournaments don't have coordinates, so we pass null
// to let the map show the default view
const mapLatitude = computed(() => null)
const mapLongitude = computed(() => null)

onMounted(() => {
  fetchTournaments()
})
</script>

<template>
  <SidebarProvider :style="sidebarStyles">
    <SidebarInset>
      <div class="flex flex-col gap-4">
        <div v-if="isLoading" class="text-center p-4">
          Loading tournaments...
        </div>
        <div v-else-if="error" class="text-center p-4 text-red-500">
          Error loading tournaments: {{ error?.message }}
        </div>
        <div v-else>
          <Tabs default-value="all" class="h-full">
            <div class="flex items-center gap-2 px-4 py-2">
              <!-- Mobile sidebar trigger -->
              <SidebarTrigger class="md:hidden" />

              <TabsList class="">
                <TabsTrigger value="all" class="">
                  {{ t('navigation.tournaments') }} <Badge class="ml-2" variant="outline">{{ filteredTournaments.length }}</Badge>
                </TabsTrigger>
                <TabsTrigger value="active" class="text-zinc-600 dark:text-zinc-200">
                  {{ t('filters.active') }} <Badge class="ml-2" variant="outline">{{ activeTournaments.length }}</Badge>
                </TabsTrigger>
                <TabsTrigger value="completed" class="text-zinc-600 dark:text-zinc-200">
                  {{ t('filters.completed') }} <Badge class="ml-2" variant="outline">{{ completedTournaments.length }}</Badge>
                </TabsTrigger>
              </TabsList>
            </div>

            <!-- Search and filters -->
            <div class="flex items-center gap-2 px-4 py-2 border-b">
              <div class="relative flex-1">
                <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  v-model="searchQuery"
                  placeholder="Search tournaments..."
                  class="pl-8"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter class="h-4 w-4" />
              </Button>
            </div>

            <TabsContent value="all" class="h-[calc(100%-10rem)] overflow-auto">
              <TournamentsList
                :items="filteredTournaments"
                :hovered-tournaments="hoveredTournaments"
                :current-tournament="currentTournament"
                @select-tournament="selectTournament"
                @hover-tournament="setHoveredTournamentId"
              />
            </TabsContent>
            <TabsContent value="active" class="h-[calc(100%-10rem)] overflow-auto">
              <TournamentsList
                :items="activeTournaments"
                :hovered-tournaments="hoveredTournaments"
                :current-tournament="currentTournament"
                @select-tournament="selectTournament"
                @hover-tournament="setHoveredTournamentId"
              />
            </TabsContent>
            <TabsContent value="completed" class="h-[calc(100%-10rem)] overflow-auto">
              <TournamentsList
                :items="completedTournaments"
                :hovered-tournaments="hoveredTournaments"
                :current-tournament="currentTournament"
                @select-tournament="selectTournament"
                @hover-tournament="setHoveredTournamentId"
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </SidebarInset>
    <Sidebar
      class="sticky top-0 h-svh border-l"
      collapsible="offcanvas"
    >
      <SidebarContent class="flex flex-col gap-0">
        <div class="mb-0 border-b border-sidebar-border pb-0">
          <TournamentDetailsWidget
            :tournament="currentTournament"
            :matches="tournamentMatches"
            :is-loading-matches="isLoadingMatches"
          />
        </div>

        <div class="mb-0 border-b border-sidebar-border pb-0">
          <div class="h-52">
            <ArcheryMap
              :latitude="mapLatitude"
              :longitude="mapLongitude"
              :matches-to-display="tournamentMatches"
              :hovered-matches="[]"
              :hovered-match-id="null"
              @select-match="handleSelectTournamentFromMap"
            />
          </div>
        </div>
      </SidebarContent>
    </Sidebar>
  </SidebarProvider>
</template>
