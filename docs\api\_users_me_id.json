{"endpoint": "/users/me/{id}", "methods": {"patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of me to update", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/me"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["users"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/me"}}}}}}}