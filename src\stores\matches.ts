import type { Params } from '@feathersjs/feathers'

import type { Match, MatchData, MatchPatch, MatchRegistration, MatchRegistrationData, MatchRegistrationPatch } from '../api/feathers-client'
import { api } from '../api/feathers-client'

// Service-only matches store - no global state, only API methods
export const useMatchesService = () => {
  // Use the typed service from the api client
  const matchesService = api.matches
  const matchRegistrationsService = api.matchRegistrations

  // Match API methods - no state management, just API calls
  async function findMatches(params?: Params) {
    try {
      const result = await matchesService.find(params)
      if (Array.isArray(result.data)) {
        return result.data
      } else {
        return result as unknown as Match[]
      }
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to fetch matches.')
      }
    }
  }

  async function getMatch(id: number) {
    try {
      const result = await matchesService.get(id)
      return result
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to fetch match with id ${id}.`)
      }
    }
  }

  async function createMatch(data: MatchData) {
    try {
      const newMatch = await matchesService.create(data)
      return newMatch
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to create match.')
      }
    }
  }

  async function patchMatch(id: number, data: MatchPatch) {
    try {
      const updatedMatch = await matchesService.patch(id, data)
      return updatedMatch
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to update match with id ${id}.`)
      }
    }
  }

  async function removeMatch(id: number) {
    try {
      const removedMatch = await matchesService.remove(id)
      return removedMatch
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to remove match with id ${id}.`)
      }
    }
  }

  // Match Registration API methods - no state management, just API calls
  async function findMatchRegistrations(params?: Params) {
    try {
      const result = await matchRegistrationsService.find(params)
      if (Array.isArray(result.data)) {
        return result.data
      } else {
        return result as unknown as MatchRegistration[]
      }
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to fetch match registrations.')
      }
    }
  }

  async function createMatchRegistration(data: MatchRegistrationData) {
    try {
      const newRegistration = await matchRegistrationsService.create(data)
      return newRegistration
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to create match registration.')
      }
    }
  }

  async function patchMatchRegistration(registrationId: number, data: MatchRegistrationPatch) {
    try {
      const updatedRegistration = await matchRegistrationsService.patch(registrationId, data)
      return updatedRegistration
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to update match registration with id ${registrationId}.`)
      }
    }
  }

  async function removeMatchRegistration(registrationId: number) {
    try {
      const removedRegistration = await matchRegistrationsService.remove(registrationId)
      return removedRegistration
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to remove match registration with id ${registrationId}.`)
      }
    }
  }

  return {
    // Match API methods
    findMatches,
    getMatch,
    createMatch,
    patchMatch,
    removeMatch,
    // Match Registration API methods
    findMatchRegistrations,
    createMatchRegistration,
    patchMatchRegistration,
    removeMatchRegistration,
  }
}
