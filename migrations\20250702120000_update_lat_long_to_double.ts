import { Knex } from 'knex';

/**
 * Migration to change latitude/longitude fields from decimal to double precision
 * in players, matches, and organizers tables.
 */
export async function up(knex: Knex): Promise<void> {
    // Players table
    await knex.schema.alterTable('players', (table) => {
        table.dropColumn('latitude');
        table.dropColumn('longitude');
    });
    await knex.schema.alterTable('players', (table) => {
        table.double('latitude');
        table.double('longitude');
    });

    // Matches table
    await knex.schema.alterTable('matches', (table) => {
        table.dropColumn('latitude');
        table.dropColumn('longitude');
    });
    await knex.schema.alterTable('matches', (table) => {
        table.double('latitude');
        table.double('longitude');
    });

    // Organizers table
    await knex.schema.alterTable('organizers', (table) => {
        table.dropColumn('latitude');
        table.dropColumn('longitude');
    });
    await knex.schema.alterTable('organizers', (table) => {
        table.double('latitude');
        table.double('longitude');
    });
}

export async function down(knex: Knex): Promise<void> {
    // Revert to decimal(9,6) or decimal(10,7) as originally defined
    await knex.schema.alterTable('players', (table) => {
        table.dropColumn('latitude');
        table.dropColumn('longitude');
    });
    await knex.schema.alterTable('players', (table) => {
        table.decimal('latitude', 9, 6);
        table.decimal('longitude', 9, 6);
    });

    await knex.schema.alterTable('matches', (table) => {
        table.dropColumn('latitude');
        table.dropColumn('longitude');
    });
    await knex.schema.alterTable('matches', (table) => {
        table.decimal('latitude', 9, 6);
        table.decimal('longitude', 9, 6);
    });

    await knex.schema.alterTable('organizers', (table) => {
        table.dropColumn('latitude');
        table.dropColumn('longitude');
    });
    await knex.schema.alterTable('organizers', (table) => {
        table.decimal('latitude', 10, 7);
        table.decimal('longitude', 10, 7);
    });
}
