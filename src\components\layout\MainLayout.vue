<script setup lang="ts">
import SidebarLeft from '@/components/layout/SidebarLeft.vue'
import {
  SidebarInset,
  SidebarProvider,
} from '@/components/ui/sidebar'
import IconBowAndArrow from '@/components/icons/IconBowAndArrow.vue'
import MainTopBar from '@/components/layout/MainTopBar.vue'

const mockGears = [
  {
    name: 'Hoyt Formula Xi',
    logo: IconBowAndArrow,
    plan: 'Olympic Recurve'
  }
]
</script>

<template>
  <SidebarProvider>
    <SidebarLeft />
    <SidebarInset>
      <MainTopBar :gears="mockGears" />
      <div class="">
        <slot></slot>
      </div>
    </SidebarInset>
  </SidebarProvider>
</template>
