{"endpoint": "/players/{id}", "methods": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of Player to return", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Player"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["players"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of Player to update", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Player"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["players"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlayerPatch"}}}}}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of Player to remove", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Player"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["players"], "security": []}}}