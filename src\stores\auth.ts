import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

import type { User } from '../api/feathers-client'
import { app } from '../api/feathers-client'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = computed(() => !!user.value)
  const isLoading = ref(false)
  const error = ref<Error | null>(null)

  async function login(credentials: Record<string, unknown>) {
    isLoading.value = true
    error.value = null
    try {
      const response = await app.authenticate(credentials)
      user.value = response.user as User
      // The router should redirect based on isAuthenticated
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('An unknown authentication error occurred.')
      }
      user.value = null
      throw err // Re-throw to be caught by the form
    } finally {
      isLoading.value = false
    }
  }

  async function logout() {
    isLoading.value = true
    error.value = null
    try {
      await app.logout()
      user.value = null
      // Clear user profile from user store if it exists
      const { useUserStore } = await import('./user')
      const userStore = useUserStore()
      userStore.clearUserProfile()
      // The router should redirect to the login page
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('An unknown error occurred during logout.')
      }
      // Potentially handle logout errors, though less common
    } finally {
      isLoading.value = false
    }
  }

  async function reAuthenticate() {
    isLoading.value = true
    error.value = null
    try {
      const response = await app.reAuthenticate()
      user.value = response.user as User
      return true
    } catch { // _err removed as it's not used
      // This is often expected if no valid token is found
      user.value = null
      return false
    } finally {
      isLoading.value = false
    }
  }

  // Attempt to re-authenticate when the store is initialized
  // This helps maintain session on page refresh
  reAuthenticate()

  return {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    reAuthenticate,
    feathersClient: app,
  }
})
