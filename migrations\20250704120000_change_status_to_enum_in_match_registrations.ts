import { Knex } from 'knex'

// Migration: Change status to enum for match_registrations (drop and recreate column)
export async function up(knex: Knex): Promise<void> {

  await knex.schema.alterTable('match_registrations', (table) => {
    table.dropColumn('status')
  })

  await knex.schema.alterTable('match_registrations', (table) => {
    table.enu('status', [
      'imported', 'draft', 'submitted', 'pending', 'reviewing', 'info', 'ineligible', 'approved', 'payment', 'paid', 'failed', 'withdrawn', 'assigned', 'ready', 'active', 'completed', 'noshow', 'disqualified', 'rejected', 'suspended', 'expired'
    ], {
      useNative: true,
      enumName: 'registration_status'
    }).notNullable().defaultTo('submitted')
  })
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable('match_registrations', (table) => {
    table.dropColumn('status')
  })

  await knex.schema.alterTable('match_registrations', (table) => {
    table.string('status').notNullable().defaultTo('pending')
  })

  await knex.schema.raw(`
    DROP TYPE IF EXISTS registration_status;
  `)
}
