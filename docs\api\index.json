[{"endpoint": "/authentication", "fileName": "_authentication.json"}, {"endpoint": "/authentication/{id}", "fileName": "_authentication_id.json"}, {"endpoint": "/oauth/{provider}", "fileName": "_oauth_provider.json"}, {"endpoint": "/oauth/{provider}/{id}", "fileName": "_oauth_provider_id.json"}, {"endpoint": "/oauth/{provider}/callback", "fileName": "_oauth_provider_callback.json"}, {"endpoint": "/_mailer", "fileName": "__mailer.json"}, {"endpoint": "/users/me", "fileName": "_users_me.json"}, {"endpoint": "/users/me/{id}", "fileName": "_users_me_id.json"}, {"endpoint": "/clubs", "fileName": "_clubs.json"}, {"endpoint": "/clubs/{id}", "fileName": "_clubs_id.json"}, {"endpoint": "/match-formats", "fileName": "_matchformats.json"}, {"endpoint": "/match-formats/{id}", "fileName": "_matchformats_id.json"}, {"endpoint": "/organizers", "fileName": "_organizers.json"}, {"endpoint": "/organizers/{id}", "fileName": "_organizers_id.json"}, {"endpoint": "/tournaments", "fileName": "_tournaments.json"}, {"endpoint": "/tournaments/{id}", "fileName": "_tournaments_id.json"}, {"endpoint": "/match-results", "fileName": "_matchresults.json"}, {"endpoint": "/match-results/{id}", "fileName": "_matchresults_id.json"}, {"endpoint": "/equipment", "fileName": "_equipment.json"}, {"endpoint": "/equipment/{id}", "fileName": "_equipment_id.json"}, {"endpoint": "/players", "fileName": "_players.json"}, {"endpoint": "/players/{id}", "fileName": "_players_id.json"}, {"endpoint": "/messages", "fileName": "_messages.json"}, {"endpoint": "/messages/{id}", "fileName": "_messages_id.json"}, {"endpoint": "/matches", "fileName": "_matches.json"}, {"endpoint": "/matches/{id}", "fileName": "_matches_id.json"}, {"endpoint": "/users", "fileName": "_users.json"}, {"endpoint": "/users/{id}", "fileName": "_users_id.json"}, {"endpoint": "/match-registrations", "fileName": "_matchregistrations.json"}, {"endpoint": "/match-registrations/{id}", "fileName": "_matchregistrations_id.json"}, {"endpoint": "/federations", "fileName": "_federations.json"}, {"endpoint": "/federations/{id}", "fileName": "_federations_id.json"}]