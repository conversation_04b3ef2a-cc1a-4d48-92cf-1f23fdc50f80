<script setup lang="ts">
import type { Tournament, Match } from '@/api/feathers-client'
import TournamentItem from './TournamentItem.vue'
import { computed } from 'vue'

// Type for tournament with populated matches
interface TournamentWithMatches extends Tournament {
  matches?: Match[]
}

// Define props
const props = defineProps<{
  items: TournamentWithMatches[]
  hoveredTournaments?: TournamentWithMatches[]
  currentTournament?: TournamentWithMatches | null
}>()

// Define emits
const emit = defineEmits<{
  selectTournament: [id: number]
  hoverTournament: [id: number | null]
}>()

const handleSelectTournament = (id: number) => {
  emit('selectTournament', id)
}

const handleHoverTournament = (id: number | null) => {
  emit('hoverTournament', id)
}

// Create enhanced items with highlighting information
const enhancedItems = computed(() => {
  const hoveredIds = new Set((props.hoveredTournaments || []).map(t => t.id))
  return props.items.map(item => ({
    ...item,
    isHighlighted: hoveredIds.has(item.id)
  }))
})
</script>

<template>
  <div class="flex flex-col gap-2 p-2 pt-0">
    <div v-if="!props.items || props.items.length === 0" class="p-8 text-center text-muted-foreground">
      No tournaments found
    </div>
    <div v-else class="space-y-2">
      <TournamentItem
        v-for="item in enhancedItems"
        :key="item.id"
        :tournament="item"
        :is-selected="props.currentTournament?.id === item.id"
        :tournament-matches="item.matches || []"
        @select="handleSelectTournament(item.id)"
        @hover="handleHoverTournament"
      />
    </div>
  </div>
</template>
