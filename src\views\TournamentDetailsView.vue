<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Trophy, Calendar, MapPin, Users } from 'lucide-vue-next'
import { useTournamentsService } from '@/stores/tournaments'
import { useMatchesService } from '@/stores/matches'
import SimpleMatchesList from '@/components/matches/SimpleMatchesList.vue'
import type { Tournament, Match, Player, MatchRegistration } from '@/api/feathers-client'
import { useI18n } from 'vue-i18n'

const route = useRoute()
const router = useRouter()
const tournamentsService = useTournamentsService()
const matchesService = useMatchesService()
const { t, locale } = useI18n()

const tournamentId = computed(() => {
  const id = route.params.id
  return typeof id === 'string' ? parseInt(id, 10) : null
})

// Local state management
const tournament = ref<Tournament | null>(null)
const matches = ref<Match[]>([])
const matchRegistrations = ref<MatchRegistration[]>([])
const isLoading = ref(false)
const error = ref<Error | null>(null)

const fetchIfNeeded = async () => {
  if (tournamentId.value && !tournament.value && !isLoading.value) {
    isLoading.value = true
    error.value = null
    try {
      tournament.value = await tournamentsService.getTournament(tournamentId.value)
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('Failed to fetch tournament')
      console.error('Failed to fetch tournament:', err)
    } finally {
      isLoading.value = false
    }
  }
}

const fetchMatchesAndRegistrations = async () => {
  if (!tournamentId.value) return;
  // Fetch matches for this tournament
  const fetchedMatches = await matchesService.findMatches({ query: { tournamentId: tournamentId.value } })
  matches.value = fetchedMatches || []
  // Fetch all registrations for matches in this tournament
  if (!matches.value.length) {
    matchRegistrations.value = []
    return
  }
  const allRegs: MatchRegistration[] = []
  await Promise.all(
    matches.value.map(async match => {
      const regs = await matchesService.findMatchRegistrations({ query: { matchId: match.id } })
      if (regs) allRegs.push(...regs)
    })
  )
  matchRegistrations.value = allRegs
}

onMounted(async () => {
  await fetchIfNeeded()
  await fetchMatchesAndRegistrations()
})
watch(tournamentId, async () => {
  await fetchIfNeeded()
  await fetchMatchesAndRegistrations()
})

// Categorize matches
const now = new Date()
const upcomingMatches = computed(() =>
  matches.value.filter(m => m.startDate && new Date(m.startDate) > now)
)
const pastMatches = computed(() =>
  matches.value.filter(m => m.endDate
    ? new Date(m.endDate) < now
    : m.startDate && new Date(m.startDate) < now
  )
)

// Aggregate unique players from all registrations
const players = computed<Player[]>(() => {
  const playerMap = new Map<number, Player>();
  for (const reg of matchRegistrations.value) {
    if (reg.player && reg.player.id && !playerMap.has(reg.player.id)) {
      playerMap.set(reg.player.id, reg.player);
    }
  }
  return Array.from(playerMap.values());
});

const goBack = () => {
  router.back()
}

const formatDate = (dateString?: string) => {
  if (!dateString) return t('common.na')
  const date = new Date(dateString)
  return date.toLocaleDateString(locale.value, { year: 'numeric', month: 'long', day: 'numeric' })
}
</script>

<template>
  <div class="min-h-screen bg-background">
    <div class="container mx-auto px-4 py-6 max-w-4xl">
      <!-- Header -->
      <div class="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="icon" @click="goBack">
          <ArrowLeft class="h-4 w-4" />
        </Button>
        <h1 class="text-2xl font-bold">{{ t('tournament.detailsTitle') }}</h1>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p class="text-muted-foreground">{{ t('tournament.loading') }}</p>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="flex items-center justify-center py-12">
        <div class="text-center">
          <p class="text-red-500 mb-4">{{ t('tournament.error') }}</p>
          <p class="text-muted-foreground">{{ error.message }}</p>
          <Button variant="outline" @click="goBack" class="mt-4">
            {{ t('common.goBack') }}
          </Button>
        </div>
      </div>

      <!-- Tournament Details -->
      <div v-else-if="tournament" class="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <Trophy class="h-5 w-5" />
              {{ tournament.name }}
            </CardTitle>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex items-center gap-2">
                <Calendar class="h-4 w-4 text-muted-foreground" />
                <span class="font-medium">{{ t('tournament.created') }}:</span>
                <span>{{ formatDate(tournament.createdAt) }}</span>
              </div>
              <div class="flex items-center gap-2">
                <Calendar class="h-4 w-4 text-muted-foreground" />
                <span class="font-medium">{{ t('tournament.completed') }}:</span>
                <span>{{ formatDate(tournament.completedAt) }}</span>
              </div>
              <div class="flex items-center gap-2">
                <MapPin class="h-4 w-4 text-muted-foreground" />
                <span class="font-medium">{{ t('tournament.federation') }}:</span>
                <span>{{ tournament.federation?.name ?? t('common.na') }}</span>
              </div>
              <div class="flex items-center gap-2">
                <Users class="h-4 w-4 text-muted-foreground" />
                <span class="font-medium">{{ t('tournament.organizer') }}:</span>
                <span>{{ tournament.organizer?.name ?? tournament.organizerId ?? t('common.na') }}</span>
              </div>
            </div>

            <div v-if="tournament.description" class="mt-4">
              <h3 class="font-medium mb-2">{{ t('tournament.description') }}</h3>
              <p class="text-muted-foreground">{{ tournament.description }}</p>
            </div>
          </CardContent>
        </Card>

        <!-- Upcoming Matches -->
        <Card v-if="upcomingMatches.length">
          <CardHeader>
            <CardTitle>{{ t('tournament.upcomingMatches') }}</CardTitle>
          </CardHeader>
          <CardContent>
            <ul>
              <li v-for="match in upcomingMatches" :key="match.id" class="mb-2">
                <span class="font-medium">{{ match.name }}</span>
                <span class="ml-2 text-muted-foreground">{{ formatDate(match.startDate) }}</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        <!-- Past Matches -->
        <Card v-if="pastMatches.length">
          <CardHeader>
            <CardTitle>{{ t('tournament.pastMatches') }}</CardTitle>
          </CardHeader>
          <CardContent>
            <SimpleMatchesList :items="pastMatches" />
          </CardContent>
        </Card>

        <!-- Players -->
        <Card v-if="players.length">
          <CardHeader>
            <CardTitle>{{ t('tournament.players') }}</CardTitle>
          </CardHeader>
          <CardContent>
            <ul>
              <li v-for="player in players" :key="player.id" class="mb-2 flex items-center gap-2">
                <span class="font-medium">{{ player.firstname }} {{ player.lastname }}</span>
                <span class="text-muted-foreground">{{ t('tournament.playerId', { id: player.id }) }}</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>

      <!-- Not Found -->
      <div v-else class="flex items-center justify-center py-12">
        <div class="text-center">
          <p class="text-muted-foreground mb-4">{{ t('tournament.notFound') }}</p>
          <Button variant="outline" @click="goBack">
            {{ t('common.goBack') }}
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
