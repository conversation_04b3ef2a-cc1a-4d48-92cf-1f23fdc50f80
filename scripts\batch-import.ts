// scripts/batch-import.ts
// Batch import script to process all imports in sequence
// Usage: pnpm run batch-import

import { spawn } from 'child_process';
import path from 'path';

async function runImportScript(script: string, csv: string) {
    return new Promise<void>((resolve, reject) => {
        const child = spawn('pnpm', ['dlx', 'tsx', path.join(__dirname, script), csv], {
            stdio: 'inherit',
            shell: process.platform === 'win32',
        });
        child.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`${script} failed with exit code ${code}`));
            }
        });
    });
}

async function main() {
    const baseDir = path.resolve(__dirname, '../../db/source');
    const steps = [
        { script: 'import-federations.ts', csv: 'federation.csv' },
        { script: 'import-match-formats.ts', csv: 'competitions_formats.csv' },
        { script: 'import-players.ts', csv: 'players.csv' },
        { script: 'import-equipment.ts', csv: 'bows.csv' },
        { script: 'import-organizers.ts', csv: 'organizers.csv' },
        { script: 'import-tournaments.ts', csv: 'tournaments.csv' },
        { script: 'import-matches.ts', csv: 'competitions.csv' },
    ];

    for (const { script, csv } of steps) {
        const csvPath = path.join(baseDir, csv);
        console.log(`\n=== Importing ${csv} with ${script} ===`);
        try {
            await runImportScript(script, csvPath);
            console.log(`✔ Imported using ${script}`);
        } catch (err) {
            console.error(`✖ Error in ${script}:`, err);
            process.exitCode = 1;
            break;
        }
    }
    console.log('\nAll imports finished.');
}

main();
