{"endpoint": "/match-results", "methods": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "schema": {"type": "integer"}}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "schema": {"type": "integer"}}, {"description": "Query parameters", "in": "query", "name": "filter", "style": "form", "explode": true, "schema": {"$ref": "#/components/schemas/MatchResultQuery"}}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchResultPagination"}}}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["match-results"], "security": []}, "post": {"parameters": [], "responses": {"201": {"description": "created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchResult"}}}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["match-results"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchResultData"}}}}}}}