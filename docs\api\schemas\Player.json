{"name": "Player", "schema": {"required": ["id"], "type": "object", "properties": {"id": {"type": "number"}, "legacyId": {"type": "number"}, "userId": {"type": "number"}, "isActive": {"type": "boolean"}, "address": {"type": "string"}, "zipcode": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "phone": {"type": "string"}, "birthdate": {"type": "string", "format": "date"}, "sex": {"type": "string"}, "firstname": {"type": "string"}, "lastname": {"type": "string"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}, "avatar": {"type": "string"}, "licenses": {}, "activatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "deletedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "number"}, "updatedBy": {"type": "number"}, "deletedBy": {"type": "number"}, "user": {"$ref": "#/components/schemas/User"}}, "additionalProperties": false}}