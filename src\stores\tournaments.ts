import type { Tournament, TournamentData, TournamentPatch } from '@/api/feathers-client'
import { api } from '@/api/feathers-client'
import type { Params } from '@feathersjs/feathers'

// Service-only tournaments store - no global state, only API methods
export const useTournamentsService = () => {
  // Use the typed service from the api client
  const tournamentsService = api.tournaments

  // Tournament API methods - no state management, just API calls
  async function findTournaments(params?: Params) {
    try {
      const result = await tournamentsService.find(params)
      if (Array.isArray(result.data)) {
        return result.data
      } else {
        return result as unknown as Tournament[]
      }
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to fetch tournaments.')
      }
    }
  }

  async function getTournament(id: number) {
    try {
      const result = await tournamentsService.get(id)
      return result
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to fetch tournament with id ${id}.`)
      }
    }
  }

  async function createTournament(data: TournamentData) {
    try {
      const newTournament = await tournamentsService.create(data)
      return newTournament
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error('Failed to create tournament.')
      }
    }
  }

  async function patchTournament(id: number, data: TournamentPatch) {
    try {
      const updatedTournament = await tournamentsService.patch(id, data)
      return updatedTournament
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to update tournament with id ${id}.`)
      }
    }
  }

  async function removeTournament(id: number) {
    try {
      const removedTournament = await tournamentsService.remove(id)
      return removedTournament
    } catch (err) {
      if (err instanceof Error) {
        throw err
      } else {
        throw new Error(`Failed to remove tournament with id ${id}.`)
      }
    }
  }

  return {
    // Tournament API methods
    findTournaments,
    getTournament,
    createTournament,
    patchTournament,
    removeTournament,
  }
}
