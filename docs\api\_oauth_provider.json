{"endpoint": "/oauth/{provider}", "methods": {"get": {"parameters": [{"in": "path", "name": "provider", "schema": {"type": "string"}, "required": true, "description": "provider parameter"}, {"description": "Number of results to return", "in": "query", "name": "$limit", "schema": {"type": "integer"}}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "schema": {"type": "integer"}}, {"description": "Property to sort results", "in": "query", "name": "$sort", "style": "deepObject", "schema": {"type": "object"}}, {"description": "Query parameters to filter", "in": "query", "name": "filter", "style": "form", "explode": true, "schema": {"$ref": "#/components/schemas/:provider"}}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/:providerList"}}}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["o<PERSON>h"], "security": []}, "post": {"parameters": [{"in": "path", "name": "provider", "schema": {"type": "string"}, "required": true, "description": "provider parameter"}], "responses": {"201": {"description": "created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/:provider"}}}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["o<PERSON>h"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/:provider"}}}}}}}