{"endpoint": "/match-formats/{id}", "methods": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of MatchFormat to return", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchFormat"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["match-formats"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of MatchFormat to update", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchFormat"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["match-formats"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchFormatPatch"}}}}}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of MatchFormat to remove", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchFormat"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["match-formats"], "security": []}}}