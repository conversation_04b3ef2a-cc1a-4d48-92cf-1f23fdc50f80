{"name": "MessageQuery", "schema": {"type": "object", "properties": {"$sort": {"type": "object", "properties": {"id": {"maximum": 1, "minimum": -1, "type": "integer"}, "text": {"maximum": 1, "minimum": -1, "type": "integer"}, "createdAt": {"maximum": 1, "minimum": -1, "type": "integer"}, "userId": {"maximum": 1, "minimum": -1, "type": "integer"}}, "additionalProperties": false}, "$select": {"maxItems": 4, "type": "array", "items": {"enum": ["id", "text", "createdAt", "userId"], "type": "string"}}, "$and": {"type": "array", "items": {"anyOf": [{"additionalProperties": false, "type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "text": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "createdAt": {"anyOf": [{"format": "date-time", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date-time", "type": "string"}, "$gte": {"format": "date-time", "type": "string"}, "$lt": {"format": "date-time", "type": "string"}, "$lte": {"format": "date-time", "type": "string"}, "$ne": {"format": "date-time", "type": "string"}, "$in": {"type": "array", "items": {"format": "date-time", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date-time", "type": "string"}}}}]}, "userId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}}}, {"type": "object", "properties": {"$or": {"type": "array", "items": {"additionalProperties": false, "type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "text": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "createdAt": {"anyOf": [{"format": "date-time", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date-time", "type": "string"}, "$gte": {"format": "date-time", "type": "string"}, "$lt": {"format": "date-time", "type": "string"}, "$lte": {"format": "date-time", "type": "string"}, "$ne": {"format": "date-time", "type": "string"}, "$in": {"type": "array", "items": {"format": "date-time", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date-time", "type": "string"}}}}]}, "userId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}}}}}, "required": ["$or"]}]}}, "$or": {"type": "array", "items": {"type": "object", "properties": {"id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "text": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "createdAt": {"anyOf": [{"format": "date-time", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date-time", "type": "string"}, "$gte": {"format": "date-time", "type": "string"}, "$lt": {"format": "date-time", "type": "string"}, "$lte": {"format": "date-time", "type": "string"}, "$ne": {"format": "date-time", "type": "string"}, "$in": {"type": "array", "items": {"format": "date-time", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date-time", "type": "string"}}}}]}, "userId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}}, "additionalProperties": false}}, "id": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}, "text": {"anyOf": [{"type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "string"}, "$gte": {"type": "string"}, "$lt": {"type": "string"}, "$lte": {"type": "string"}, "$ne": {"type": "string"}, "$in": {"type": "array", "items": {"type": "string"}}, "$nin": {"type": "array", "items": {"type": "string"}}}}]}, "createdAt": {"anyOf": [{"format": "date-time", "type": "string"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"format": "date-time", "type": "string"}, "$gte": {"format": "date-time", "type": "string"}, "$lt": {"format": "date-time", "type": "string"}, "$lte": {"format": "date-time", "type": "string"}, "$ne": {"format": "date-time", "type": "string"}, "$in": {"type": "array", "items": {"format": "date-time", "type": "string"}}, "$nin": {"type": "array", "items": {"format": "date-time", "type": "string"}}}}]}, "userId": {"anyOf": [{"type": "number"}, {"additionalProperties": false, "type": "object", "properties": {"$gt": {"type": "number"}, "$gte": {"type": "number"}, "$lt": {"type": "number"}, "$lte": {"type": "number"}, "$ne": {"type": "number"}, "$in": {"type": "array", "items": {"type": "number"}}, "$nin": {"type": "array", "items": {"type": "number"}}}}]}}, "additionalProperties": false}}