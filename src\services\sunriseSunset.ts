// Service for interacting with the SunriseSunset.io API
// https://api.sunrisesunset.io/

/**
 * Formats a date object or string to YYYY-MM-DD format for API use
 * @param dateString Date string or Date object
 * @returns Formatted date string, or null if invalid
 */
export function formatDateForApi(dateString?: string | Date): string | null {
  if (!dateString) return null;
  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  } catch (e) {
    console.error("Error formatting date for API:", e);
    return null;
  }
}

/**
 * Converts 12h time format (e.g., "5:51:25 AM") to 24h format without seconds (e.g., "05:51")
 * @param timeString Time string in 12h format
 * @returns Time string in 24h format without seconds
 */
export function formatTimeTo24h(timeString: string): string {
  if (!timeString || timeString === 'Loading...' || timeString.includes('N/A')) {
    return timeString; // Return unchanged if it's a status message
  }

  try {
    // Parse time string (format example: "5:51:25 AM")
    const match = timeString.match(/(\d+):(\d+):(\d+)\s+(AM|PM)/i);
    if (!match) return timeString; // Return unchanged if format doesn't match

    let hours = parseInt(match[1]);
    const minutes = match[2];
    const ampm = match[4].toUpperCase();

    // Convert to 24-hour format
    if (ampm === 'PM' && hours < 12) {
      hours += 12;
    } else if (ampm === 'AM' && hours === 12) {
      hours = 0;
    }

    // Format with leading zeros for hours
    return `${hours.toString().padStart(2, '0')}:${minutes}`;
  } catch (e) {
    console.error("Error formatting time:", e, timeString);
    return timeString; // Return original on error
  }
}

export interface SunriseSunsetResult {
  date: string;
  sunrise: string; // Formatted as 24h time by default, e.g., "05:51"
  sunset: string; // Formatted as 24h time by default, e.g., "20:21"
  firstLight?: string;
  lastLight?: string;
  dawn?: string;
  dusk?: string;
  solarNoon?: string;
  goldenHour?: string;
  dayLength?: string;
  timezone?: string;
  utcOffset?: number;
  raw?: any; // The full, unprocessed response data
}

export interface SunriseSunsetError {
  message: string;
  code?: string;
  details?: string;
}

/**
 * Fetch sunrise and sunset times for a specific latitude, longitude and date
 *
 * @param latitude The latitude coordinate
 * @param longitude The longitude coordinate
 * @param date Optional date string in YYYY-MM-DD format (defaults to today if not provided)
 * @returns An object containing sunrise, sunset times, and other related data
 */
export async function fetchSunriseSunset(
  latitude: number,
  longitude: number,
  date?: string
): Promise<SunriseSunsetResult | SunriseSunsetError> {
  if (!latitude || !longitude) {
    return {
      message: 'Missing coordinates',
      code: 'MISSING_COORDINATES'
    };
  }

  const apiUrlDate = date ? `&date=${date}` : '';
  const apiUrl = `https://api.sunrisesunset.io/json?lat=${latitude}&lng=${longitude}${apiUrlDate}`;

  try {
    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    if (data.status === 'OK' && data.results) {
      const results = data.results;

      return {
        date: results.date,
        sunrise: formatTimeTo24h(results.sunrise),
        sunset: formatTimeTo24h(results.sunset),
        firstLight: results.first_light ? formatTimeTo24h(results.first_light) : undefined,
        lastLight: results.last_light ? formatTimeTo24h(results.last_light) : undefined,
        dawn: results.dawn ? formatTimeTo24h(results.dawn) : undefined,
        dusk: results.dusk ? formatTimeTo24h(results.dusk) : undefined,
        solarNoon: results.solar_noon ? formatTimeTo24h(results.solar_noon) : undefined,
        goldenHour: results.golden_hour ? formatTimeTo24h(results.golden_hour) : undefined,
        dayLength: results.day_length,
        timezone: results.timezone,
        utcOffset: results.utc_offset,
        raw: data // Include raw data for debugging or advanced use
      };
    } else {
      throw new Error(data.status || 'API returned no results');
    }
  } catch (err) {
    const error = err as Error;
    console.error('Failed to fetch sunrise/sunset times:', error);
    return {
      message: error.message || 'Unknown error',
      details: error.toString()
    };
  }
}

// Named constants for error cases
export const SunriseSunsetErrorMessages = {
  LOADING: 'Loading...',
  LOCATION_MISSING: 'N/A (loc missing)',
  API_ERROR: 'N/A (API err)'
};
