import { createSwaggerServiceOptions } from 'feathers-swagger'

// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'
import { hooks as schemaHooks } from '@feathersjs/schema'

import type { Application } from '../../declarations'
import { populate } from '../../hooks/populate'
import { populateUserTracking, skipIfDeletedByHook } from '../../hooks/user-tracking'
import { validateRegistration } from './hooks/validate-registration'
import { MatchRegistrationsService, getOptions } from './match-registrations.class'
import {
  matchRegistrationDataResolver,
  matchRegistrationDataSchema,
  matchRegistrationDataValidator,
  matchRegistrationExternalResolver,
  matchRegistrationPatchResolver,
  matchRegistrationPatchSchema,
  matchRegistrationPatchValidator,
  matchRegistrationQueryResolver,
  matchRegistrationQuerySchema,
  matchRegistrationQueryValidator,
  matchRegistrationResolver,
  matchRegistrationSchema
} from './match-registrations.schema'
import { matchRegistrationsMethods, matchRegistrationsPath } from './match-registrations.shared'

export { MatchRegistrationsService } from './match-registrations.class'

// Configure populate relationships
const populateConfig = {
  player: {
    service: 'players',
    localKey: 'playerId',
    foreignKey: 'id',
    asArray: false,
    select: ['id', 'firstname', 'lastname', 'sex', 'city', 'country']
  },
  match: {
    service: 'matches',
    localKey: 'matchId',
    foreignKey: 'id',
    asArray: false,
    select: ['id', 'name', 'startDate', 'endDate', 'organizerId', 'tournamentId', 'federationId']
  }
}
// A configure function that registers the service and its hooks via `app.configure`
export const matchRegistrations = (app: Application) => {
  // Register our service on the Feathers application
  const serviceOptions = {
    ...getOptions(app),
    paginate: { default: 1000 }
  }
  app.use(matchRegistrationsPath, new MatchRegistrationsService(serviceOptions), {
    // A list of all methods this service exposes externally
    methods: matchRegistrationsMethods,
    // You can add additional custom events to be sent to clients here
    events: [],
    docs: createSwaggerServiceOptions({
      schemas: { matchRegistrationDataSchema, matchRegistrationQuerySchema, matchRegistrationSchema, matchRegistrationPatchSchema }, // Corrected: singular schema names
      docs: {
        description: 'Match registrations service',
        securities: ['all']
      }
    })
  })
  // Initialize hooks
  app.service(matchRegistrationsPath).hooks({
    around: {
      all: [
        authenticate('jwt'),
        schemaHooks.resolveExternal(matchRegistrationExternalResolver),
        schemaHooks.resolveResult(matchRegistrationResolver)
      ]
    },
    before: {
      all: [
        skipIfDeletedByHook(),
        schemaHooks.validateQuery(matchRegistrationQueryValidator),
        schemaHooks.resolveQuery(matchRegistrationQueryResolver)
      ],
      find: [populate(populateConfig)],
      get: [populate(populateConfig)],
      create: [
        // validateRegistration(),
        schemaHooks.validateData(matchRegistrationDataValidator),
        schemaHooks.resolveData(matchRegistrationDataResolver),
        populateUserTracking()
      ],
      patch: [
        schemaHooks.validateData(matchRegistrationPatchValidator),
        schemaHooks.resolveData(matchRegistrationPatchResolver),
        populateUserTracking()
      ],
      remove: [populateUserTracking()]
    },
    after: {
      all: [],
      find: [populate(populateConfig)],
      get: [populate(populateConfig)]
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations' {
  interface ServiceTypes {
    [matchRegistrationsPath]: MatchRegistrationsService
  }
}
