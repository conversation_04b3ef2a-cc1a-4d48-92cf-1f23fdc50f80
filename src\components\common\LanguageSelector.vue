<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'

interface Language {
  code: string
  label: string
  flag: string
}

const languages: Language[] = [
  { code: 'pl', label: 'Polski', flag: '/flags/pl.svg' },
  { code: 'en', label: 'English', flag: '/flags/gb.svg' },
  { code: 'cs', label: 'Češ<PERSON>', flag: '/flags/cz.svg' },
  // Note: These languages don't have locale files yet, but keeping for future expansion
  // { code: 'sk', label: 'Slovenský', flag: '/flags/sk.svg' },
  // { code: 'hu', label: 'Magyar', flag: '/flags/hu.svg' },
  // { code: 'de', label: 'Deutsch', flag: '/flags/de.svg' },
  // { code: 'fr', label: 'Français', flag: '/flags/fr.svg' }
]

const { locale } = useI18n()

const selectedLanguage = computed(() => {
  return languages.find(lang => lang.code === locale.value) || languages[0]
})

function selectLanguage(lang: Language) {
  locale.value = lang.code
}
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger as-child>
      <Button variant="ghost" size="icon" class="relative">
        <img
          :src="selectedLanguage.flag"
          :alt="selectedLanguage.code"
          class="h-4 w-4 rounded-full mr-1 object-cover"
        />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end">
      <DropdownMenuItem
        v-for="lang in languages"
        :key="lang.code"
        @click="selectLanguage(lang)"
        :class="{'font-bold': lang.code === selectedLanguage.code}"
      >
        <img
          :src="lang.flag"
          :alt="lang.code"
          class="h-5 w-5 rounded-full mr-2 object-cover inline-block"
        />
        {{ lang.label }}
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
