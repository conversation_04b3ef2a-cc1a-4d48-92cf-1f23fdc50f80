# Archery Points - Sports Tracking App

## Project Overview

This is a sports tracking application built with Vue 3 that allows:

- Tracking matches results and statistics
- Users to sign up for matches
- Competition organizers to create and manage events

## Tech Stack

- Vue 3
- TypeScript
- Pinia
- Vue Router
- Tailwind CSS 4
- shadcn-vue and Reca <PERSON>I for additional UI components
- axios
- pnpm (also use pnpm dlx intead npx)
- Feathers.js 5 for the API with typed client 'ap-api-feathers'

## Folder Structure

src/
├── assets/ # Static assets like images, fonts, etc.
├── components/ # Reusable components
│ ├── ui/ # Base UI components from shadcn/reca
│ ├── common/
│ └── layout/
├── composables/ # Shared composable functions
├── services/ # Feature-based services
├── router/
├── stores/
├── api/ # Feathers client
├── types/
├── utils/
└── views/

## Backend API

The backend API is built with Feathers.js.
Client is configured in `src/api/feathers-client.ts`.
For each service, use separate pinia store in `src/stores/`.
For data objects from APIuse types from typed client `ap-api-feathers` reexported in `src/api/feathers-client.ts`.
API objects schemas are located in /docs/api/schemas with schemasIndex.json for reference.

## Authentication

Authentication is handled by the Feathers.js backend using @feathersjs/authentication-client.
The client is configured in `src/api/feathers-client.ts`.
Auth store is in `src/stores/auth.ts`.

## Coding Conventions

### Vue Components

- Use Composition API with `<script setup>` syntax
- Component file names should use PascalCase
- Component names should be multi-word to avoid conflicts with HTML elements
- Props should be explicitly typed with TypeScript
- Emit events using defineEmits with TypeScript types
- prefer v-model binding over poperties if possible

### State Management

- Use Pinia stores for global state management
- Create separate stores for different domains (auth, matches, athletes, etc.)
- Use TypeScript for store state, getters, and actions
- Prefer composition API style for Pinia stores

### Styling

- Use Tailwind CSS utility classes for styling
- Follow a mobile-first approach
- Use shadcn-vue and Reca UI components when possible
- Customize component themes through Tailwind configuration
- Use CSS variables for theming

### Routing

- Use named routes for navigation
- Group routes by feature
- Implement route guards for authentication
- Use route meta fields for additional route information

### translations

use vue-i18n translation files (/src/i18n/locales).
When adding new texts, add only english tranlations if not prompted otherwise.
Use useI18n() composable, destructured t function, not global $t
Prefer translations in templates, not script block. If tranbslations are needed in script block, use computed properties.

## Best Practices

- Create comments only complex logic
- Use TypeScript for type safety
- Write unit tests for components and stores
- Use composables for reusable logic
- Follow the Single Responsibility Principle
- Use async/await for asynchronous operations
- Implement proper error handling
- Use environment variables for configuration
- Optimize components with v-memo and shallowRef when appropriate
- Implement proper form validation
- Use Vue's built-in transition components for animations
- Render dates and timestamps in human readable format
- always use i18n translations for hardcoded texts
