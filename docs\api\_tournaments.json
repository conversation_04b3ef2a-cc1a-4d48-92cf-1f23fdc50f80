{"endpoint": "/tournaments", "methods": {"get": {"parameters": [{"description": "Number of results to return", "in": "query", "name": "$limit", "schema": {"type": "integer"}}, {"description": "Number of results to skip", "in": "query", "name": "$skip", "schema": {"type": "integer"}}, {"description": "Query parameters", "in": "query", "name": "filter", "style": "form", "explode": true, "schema": {"$ref": "#/components/schemas/TournamentQuery"}}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TournamentPagination"}}}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Retrieves a list of all resources from the service.", "summary": "", "tags": ["tournaments"], "security": []}, "post": {"parameters": [], "responses": {"201": {"description": "created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tournament"}}}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["tournaments"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TournamentData"}}}}}}}