{"endpoint": "/authentication/{id}", "methods": {"delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of Authentication to remove", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Authentication"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["auth"], "security": []}}}