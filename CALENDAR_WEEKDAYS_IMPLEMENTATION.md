# Calendar Weekdays Implementation

## Overview

The EventCalendar component has been updated to use the browser's built-in `Intl.DateTimeFormat` API for localized weekday names instead of hardcoded translations. This approach provides automatic support for all locales without manual maintenance.

## Changes Made

### 1. Updated EventCalendar.vue

- Replaced the hardcoded translation-based `weekDaysShort` computed property with a new implementation using `Intl.DateTimeFormat`
- Added a helper function `getWeekdays()` that generates localized weekday names
- Starts the week on Monday (as specified by `:week-starts-on="1"`)
- Automatically updates when the locale changes

### 2. Removed Hardcoded Translations

- Cleaned up the calendar translations in all locale files:
  - `src/i18n/locales/en.json`
  - `src/i18n/locales/cs.json`
  - `src/i18n/locales/pl.json`

## Implementation Details

### Helper Function

```typescript
const getWeekdays = (localeCode: string, weekday: 'long' | 'short' | 'narrow' = 'short') => {
  const formatter = new Intl.DateTimeFormat(localeCode, { weekday })
  // Start from Monday (2021-08-02 is a Monday)
  const baseDate = new Date(Date.UTC(2021, 7, 2))
  return Array.from({ length: 7 }).map((_, i) =>
    formatter.format(new Date(baseDate.getTime() + i * 24 * 60 * 60 * 1000)),
  )
}
```

### Reactive Weekday Names

```typescript
const weekDaysShort = computed(() => {
  // Use Intl.DateTimeFormat for localized weekday names
  return getWeekdays(locale.value, 'short')
})
```

## Benefits

1. **Automatic Localization**: Supports all locales without manual translation
2. **Maintenance-Free**: No need to maintain weekday translations for new locales
3. **Consistent**: Uses browser's native locale formatting
4. **Reactive**: Automatically updates when the user changes language
5. **Flexible**: Easy to switch between 'long', 'short', or 'narrow' formats

## Usage Examples

The weekday names will now automatically display in the correct locale:

- **English (en)**: Mon, Tue, Wed, Thu, Fri, Sat, Sun
- **Czech (cs)**: po, út, st, čt, pá, so, ne
- **Polish (pl)**: pon., wt., śr., czw., pt., sob., niedz.

## Future Enhancements

The helper function can be easily extended to support:

- Different week start days based on locale
- Long weekday names for different calendar views
- Narrow weekday names for compact layouts
