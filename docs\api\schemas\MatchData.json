{"name": "MatchData", "schema": {"required": ["name", "isActive"], "type": "object", "properties": {"legacyId": {"type": "number"}, "name": {"type": "string"}, "isActive": {"type": "boolean"}, "country": {"type": "string"}, "city": {"type": "string"}, "postcode": {"type": "string"}, "address": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}, "description": {"type": "string"}, "styleDivisions": {}, "ageDivisions": {}, "forWomen": {"type": "boolean"}, "forMen": {"type": "boolean"}, "organizerId": {"type": "number"}, "maxPlayersAmount": {"type": "number"}, "tournamentId": {"type": "number"}, "tournamentConfirmedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}}}}