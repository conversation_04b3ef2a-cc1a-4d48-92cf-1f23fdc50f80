import { watch } from 'vue'
import { createI18n } from 'vue-i18n'

// Dynamically import all locale JSON files in the locales folder
const localeFiles = import.meta.glob('./locales/*.json', { eager: true }) as Record<string, { default: Record<string, string> }>

const messages: Record<string, Record<string, string>> = {}
for (const path in localeFiles) {
  // Extract locale code from filename, e.g. './locales/en.json' => 'en'
  const match = path.match(/([\\/])([a-zA-Z-_]+)\.json$/)
  if (match) {
    const locale = match[2]
    messages[locale] = localeFiles[path].default
  }
}

const getDefaultLocale = () => {
  // 1. Check localStorage
  const stored = localStorage.getItem('ap-locale')
  if (stored && messages[stored]) return stored
  // 2. Check browser/system
  const browser = navigator.language.split('-')[0]
  if (messages[browser]) return browser
  // 3. Fallback
  return 'en'
}

const i18n = createI18n({
  legacy: false,
  locale: getDefaultLocale(),
  fallbackLocale: 'en',
  messages
})

// Helper to change language and persist
export function setLocale(locale: string) {
  if (messages[locale]) {
    i18n.global.locale.value = locale
    localStorage.setItem('ap-locale', locale)
  }
}

// Automatically persist locale changes
watch(
  () => i18n.global.locale.value,
  (newLocale) => {
    if (messages[newLocale]) {
      localStorage.setItem('ap-locale', newLocale)
    }
  }
)

export default i18n
