// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'

import type { ClientApplication } from '../../client'
import type { Federation, FederationData, FederationPatch, FederationQuery } from './federations.class'
import { FederationService } from './federations.class'

export type { Federation, FederationData, FederationPatch, FederationQuery }

export type FederationClientService = Pick<FederationService<Params<FederationQuery>>, (typeof federationsMethods)[number]>

export const federationsPath = 'federations'

export const federationsMethods: Array<keyof FederationService> = ['find', 'get', 'create', 'patch', 'remove']

export const federationsClient = (client: ClientApplication) => {
    const connection = client.get('connection')

    client.use(federationsPath, connection.service(federationsPath), {
        methods: federationsMethods
    })
}

// Add this service to the client service type index
declare module '../../client' {
    interface ServiceTypes {
        [federationsPath]: FederationClientService
    }
}
