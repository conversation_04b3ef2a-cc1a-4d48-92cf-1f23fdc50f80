{"name": "ap-client", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/", "docs:api": "node scripts/split-swagger.js"}, "dependencies": {"@feathersjs/authentication-client": "^5.0.34", "@feathersjs/feathers": "^5.0.34", "@feathersjs/rest-client": "^5.0.34", "@feathersjs/socketio-client": "^5.0.34", "@internationalized/date": "^3.8.1", "@tailwindcss/vite": "^4.1.3", "@tanstack/vue-table": "^8.21.3", "@types/leaflet": "^1.9.17", "@vueuse/core": "^13.1.0", "ap-api-feathers": "http:/localhost:3030/ap-api-feathers-0.1.9.tgz", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "leaflet": "^1.9.4", "lucide-vue-next": "^0.523.0", "pinia": "^3.0.1", "reka-ui": "^2.2.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.2.5", "vaul-vue": "^0.4.1", "vue": "^3.5.13", "vue-i18n": "^11.1.9", "vue-router": "^4.5.0"}, "devDependencies": {"@intlify/unplugin-vue-i18n": "^6.0.8", "@playwright/test": "^1.51.1", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.1", "@vitejs/plugin-vue": "^5.2.3", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@10.12.4"}