<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import type { Tournament, Match } from '@/api/feathers-client'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  Trophy,
  Calendar,
  Users,
  MapPin,
  Building,
  Info as InfoIcon,
  ExternalLink,
  Target,
  Loader2
} from 'lucide-vue-next'

const props = defineProps<{
  tournament?: Tournament | null
  matches?: Match[]
  isLoadingMatches?: boolean
}>()

const router = useRouter()
const { t, locale } = useI18n()

const formatDate = (dateString?: string) => {
  if (!dateString) return t('common.na')
  const date = new Date(dateString)
  return date.toLocaleDateString(locale.value, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const goToTournamentDetails = () => {
  if (props.tournament?.id) {
    router.push({ name: 'tournament-details', params: { id: props.tournament.id.toString() } })
  }
}

const organizerName = computed(() => {
  return props.tournament?.organizer?.name || `Organizer ${props.tournament?.organizerId}` || t('common.na')
})

const federationName = computed(() => {
  return props.tournament?.federation?.name || t('common.na')
})

const statusBadge = computed(() => {
  if (!props.tournament) return null

  if (props.tournament.isActive) {
    return { text: 'Active', variant: 'default' as const, class: 'bg-green-100 text-green-800' }
  } else if (props.tournament.completedAt) {
    return { text: 'Completed', variant: 'secondary' as const, class: 'bg-gray-100 text-gray-800' }
  } else {
    return { text: 'Inactive', variant: 'outline' as const, class: 'bg-yellow-100 text-yellow-800' }
  }
})

const roundsInfo = computed(() => {
  if (!props.tournament) return null

  const total = props.tournament.totalRounds || 0
  const min = props.tournament.minRounds || 0

  if (total === 0) return null

  return {
    total,
    min,
    optional: total - min
  }
})

const upcomingMatches = computed(() => {
  if (!props.matches) return []
  const now = new Date()
  return props.matches.filter(match =>
    match.startDate && new Date(match.startDate) > now
  ) // Show all upcoming matches
})

const pastMatches = computed(() => {
  if (!props.matches) return []
  const now = new Date()
  return props.matches.filter(match =>
    match.endDate ? new Date(match.endDate) < now :
    match.startDate && new Date(match.startDate) < now
  ) // Show all past matches
})

const goToMatch = (matchId: number) => {
  router.push({ name: 'match-details', params: { id: matchId.toString() } })
}
</script>

<template>
  <div class="p-4">
    <div v-if="!tournament" class="text-center text-muted-foreground">
      <Trophy class="h-8 w-8 mx-auto mb-2 opacity-50" />
      <p class="text-sm">{{ t('tournament.selectToView') }}</p>
    </div>

    <div v-else class="space-y-4">
      <!-- Tournament Header -->
      <div class="space-y-2">
        <div class="flex items-start justify-between gap-2">
          <h3 class="font-semibold text-base leading-tight">{{ tournament.name }}</h3>
          <Badge v-if="statusBadge" :variant="statusBadge.variant" :class="statusBadge.class">
            {{ statusBadge.text }}
          </Badge>
        </div>

        <!-- Tournament ID and quick info -->
        <div class="flex items-center gap-2 text-xs text-muted-foreground">
          <Trophy class="h-3 w-3" />
          <span>ID: {{ tournament.id }}</span>
        </div>
      </div>

      <Separator />

      <!-- Tournament Details -->
      <div class="space-y-3">
        <!-- Organizer -->
        <div class="flex items-center gap-2 text-sm">
          <Building class="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div class="min-w-0 flex-1">
            <span class="text-muted-foreground">{{ t('tournament.organizer') }}:</span>
            <span class="ml-1 font-medium">{{ organizerName }}</span>
          </div>
        </div>

        <!-- Federation -->
        <div v-if="federationName !== 'N/A'" class="flex items-center gap-2 text-sm">
          <MapPin class="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div class="min-w-0 flex-1">
            <span class="text-muted-foreground">{{ t('tournament.federation') }}:</span>
            <span class="ml-1 font-medium">{{ federationName }}</span>
          </div>
        </div>

        <!-- Created Date -->
        <div class="flex items-center gap-2 text-sm">
          <Calendar class="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div class="min-w-0 flex-1">
            <span class="text-muted-foreground">{{ t('tournament.created') }}:</span>
            <span class="ml-1 font-medium">{{ formatDate(tournament.createdAt) }}</span>
          </div>
        </div>

        <!-- Completed Date -->
        <div v-if="tournament.completedAt" class="flex items-center gap-2 text-sm">
          <Calendar class="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div class="min-w-0 flex-1">
            <span class="text-muted-foreground">{{ t('tournament.completed') }}:</span>
            <span class="ml-1 font-medium">{{ formatDate(tournament.completedAt) }}</span>
          </div>
        </div>

        <!-- Rounds Information -->
        <div v-if="roundsInfo" class="flex items-center gap-2 text-sm">
          <Users class="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div class="min-w-0 flex-1">
            <span class="text-muted-foreground">{{ t('tournament.rounds') }}:</span>
            <span class="ml-1 font-medium">
              {{ roundsInfo.total }} total
              <span v-if="roundsInfo.min > 0" class="text-muted-foreground">
                ({{ roundsInfo.min }} required)
              </span>
            </span>
          </div>
        </div>
      </div>

      <!-- Description -->
      <div v-if="tournament.description" class="space-y-2">
        <Separator />
        <div class="space-y-1">
          <div class="flex items-center gap-2">
            <InfoIcon class="h-4 w-4 text-muted-foreground" />
            <span class="text-sm font-medium">{{ t('tournament.description') }}</span>
          </div>
          <p class="text-sm text-muted-foreground leading-relaxed pl-6">
            {{ tournament.description }}
          </p>
        </div>
      </div>

      <!-- Tournament Matches -->
      <div class="space-y-2">
        <Separator />
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <Target class="h-4 w-4 text-muted-foreground" />
            <span class="text-sm font-medium">{{ t('tournament.matches') }}</span>
            <Loader2 v-if="isLoadingMatches" class="h-3 w-3 animate-spin text-muted-foreground" />
          </div>

          <div v-if="!matches || matches.length === 0" class="text-xs text-muted-foreground pl-6">
            {{ isLoadingMatches ? t('common.loading') : t('tournament.noMatches') }}
          </div>

          <div v-else class="space-y-2">
            <!-- Upcoming Matches -->
            <div v-if="upcomingMatches.length > 0" class="space-y-1">
              <div class="text-xs font-medium text-muted-foreground pl-6">{{ t('tournament.upcomingMatches') }}</div>
              <div class="space-y-1 pl-6">
                <div
                  v-for="match in upcomingMatches"
                  :key="match.id"
                  class="flex items-center justify-between text-xs p-1 rounded hover:bg-accent/50 cursor-pointer"
                  @click="goToMatch(match.id)"
                >
                  <div class="min-w-0 flex-1">
                    <div class="font-medium truncate">{{ match.name }}</div>
                    <div class="text-muted-foreground">{{ formatDate(match.startDate) }}</div>
                  </div>
                  <ExternalLink class="h-3 w-3 text-muted-foreground flex-shrink-0" />
                </div>
              </div>
            </div>

            <!-- Past Matches -->
            <div v-if="pastMatches.length > 0" class="space-y-1">
              <div class="text-xs font-medium text-muted-foreground pl-6">{{ t('tournament.pastMatches') }}</div>
              <div class="space-y-1 pl-6">
                <div
                  v-for="match in pastMatches"
                  :key="match.id"
                  class="flex items-center justify-between text-xs p-1 rounded hover:bg-accent/50 cursor-pointer opacity-75"
                  @click="goToMatch(match.id)"
                >
                  <div class="min-w-0 flex-1">
                    <div class="font-medium truncate">{{ match.name }}</div>
                    <div class="text-muted-foreground">{{ formatDate(match.startDate) }}</div>
                  </div>
                  <ExternalLink class="h-3 w-3 text-muted-foreground flex-shrink-0" />
                </div>
              </div>
            </div>

            <!-- Show total count -->
            <div v-if="matches && matches.length > 0" class="text-xs text-muted-foreground pl-6">
              {{ t('tournament.totalMatches', { count: matches.length }) }}
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="space-y-2">
        <Separator />
        <Button
          variant="outline"
          size="sm"
          class="w-full"
          @click="goToTournamentDetails"
        >
          <ExternalLink class="h-4 w-4 mr-2" />
          {{ t('tournament.viewDetails') }}
        </Button>
      </div>
    </div>
  </div>
</template>
