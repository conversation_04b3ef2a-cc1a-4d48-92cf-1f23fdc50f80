{"endpoint": "/_mailer", "methods": {"post": {"parameters": [], "responses": {"201": {"description": "created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/_mailer"}}}}, "401": {"description": "not authenticated"}, "500": {"description": "general error"}}, "description": "Creates a new resource with data.", "summary": "", "tags": ["_mailer"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/_mailer"}}}}}}}