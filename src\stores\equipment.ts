import { defineStore } from 'pinia'
import { ref } from 'vue'

import type { EquipmentData, EquipmentPatch } from '../api/feathers-client'
import { api } from '../api/feathers-client'
import { useAuthStore } from './auth'
import { useUserStore } from './user'

/**
 * Equipment store for managing user's equipment/gear CUD actions.
 * The equipment list is now managed within the user store.
 * This store handles creating, updating, and deleting equipment and then
 * triggers a refresh of the user profile.
 *
 * Usage:
 * ```ts
 * const equipmentStore = useEquipmentStore()
 *
 * // Create new equipment
 * await equipmentStore.createEquipment({ name: 'My Bow', category: 'Recurve' })
 *
 * // Update equipment
 * await equipmentStore.updateEquipment(id, { name: 'My New Bow' })
 *
 * // Delete equipment
 * await equipmentStore.deleteEquipment(id)
 * ```
 */

export const useEquipmentStore = defineStore('equipment', () => {
  // Use the typed service from the api client
  const equipmentService = api.equipment

  const isLoading = ref(false)
  const error = ref<Error | null>(null)

  async function createEquipment(data: EquipmentData) {
    const authStore = useAuthStore()
    const userStore = useUserStore()

    if (!authStore.isAuthenticated) {
      error.value = new Error('User not authenticated')
      return
    }

    // Use the active player ID if not specified
    if (!data.playerId && userStore.activePlayer) {
      data.playerId = userStore.activePlayer.id
    }

    isLoading.value = true
    error.value = null

    try {
      const newEquipment = await equipmentService.create(data)
      // Refresh user profile to get the updated equipment list
      await userStore.fetchUserProfile()
      return newEquipment
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to create equipment')
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function updateEquipment(id: number, data: EquipmentPatch) {
    const userStore = useUserStore()
    isLoading.value = true
    error.value = null

    try {
      const updatedEquipment = await equipmentService.patch(id, data)
      // Refresh user profile to get the updated equipment list
      await userStore.fetchUserProfile()
      return updatedEquipment
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to update equipment')
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function deleteEquipment(id: number) {
    const userStore = useUserStore()
    isLoading.value = true
    error.value = null

    try {
      await equipmentService.remove(id)
      // Refresh user profile to get the updated equipment list
      await userStore.fetchUserProfile()
    } catch (err) {
      if (err instanceof Error) {
        error.value = err
      } else {
        error.value = new Error('Failed to delete equipment')
      }
      throw err
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    error,
    createEquipment,
    updateEquipment,
    deleteEquipment,
  }
})
