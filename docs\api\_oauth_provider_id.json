{"endpoint": "/oauth/{provider}/{id}", "methods": {"get": {"parameters": [{"in": "path", "name": "provider", "schema": {"type": "string"}, "required": true, "description": "provider parameter"}, {"in": "path", "name": "id", "description": "ID of :provider to return", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/:provider"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["o<PERSON>h"], "security": []}}}