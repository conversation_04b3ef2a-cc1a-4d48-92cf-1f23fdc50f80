{"endpoint": "/messages/{id}", "methods": {"get": {"parameters": [{"in": "path", "name": "id", "description": "ID of Message to return", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Retrieves a single resource with the given id from the service.", "summary": "", "tags": ["messages"], "security": []}, "patch": {"parameters": [{"in": "path", "name": "id", "description": "ID of Message to update", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Updates the resource identified by id using data.", "summary": "", "tags": ["messages"], "security": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessagePatch"}}}}}, "delete": {"parameters": [{"in": "path", "name": "id", "description": "ID of Message to remove", "schema": {"type": "integer"}, "required": true}], "responses": {"200": {"description": "success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Message"}}}}, "401": {"description": "not authenticated"}, "404": {"description": "not found"}, "500": {"description": "general error"}}, "description": "Removes the resource with id.", "summary": "", "tags": ["messages"], "security": []}}}