# Services Documentation

## SunriseSunset Service

The SunriseSunset service provides functionality to interact with the SunriseSunset.io API, allowing your application to fetch accurate sunrise, sunset, and other astronomical data based on geographic coordinates.

### Features

- Fetch sunrise and sunset times for specific locations and dates
- Format times from 12-hour to 24-hour format
- Cache API responses to reduce API calls
- Determine if a specific time is during daylight or nighttime
- Check for golden hour conditions (sunrise and sunset)
- Get human-readable descriptions of light conditions

### API Reference

#### Basic Functions

```typescript
// Format a date for API use (YYYY-MM-DD)
formatDateForApi(dateString?: string | Date): string | null

// Convert 12h time format ("5:51:25 AM") to 24h format ("05:51")
formatTimeTo24h(timeString: string): string

// Direct API call to fetch sunrise/sunset data
fetchSunriseSunset(
  latitude: number,
  longitude: number,
  date?: string
): Promise<SunriseSunsetResult | SunriseSunsetError>

// Fetch with caching to reduce API calls
fetchSunriseSunsetWithCache(
  latitude: number,
  longitude: number,
  date?: string
): Promise<SunriseSunsetResult | SunriseSunsetError>
```

#### Helper Functions

```typescript
// Check if a specific time is during daylight
isDaylight(
  latitude: number,
  longitude: number,
  dateTime?: Date
): Promise<boolean | null>

// Check if a time is during golden hour
isGoldenHour(
  latitude: number,
  longitude: number,
  dateTime?: Date
): Promise<{ isMorningGoldenHour: boolean; isEveningGoldenHour: boolean } | null>

// Get a human-readable description of the light conditions
getLightConditionDescription(
  latitude: number,
  longitude: number,
  dateTime?: Date
): Promise<string>
```

#### Constants

```typescript
// Error messages for common error states
SunriseSunsetErrorMessages = {
  LOADING: 'Loading...',
  LOCATION_MISSING: 'N/A (loc missing)',
  API_ERROR: 'N/A (API err)',
}
```

### Example Usage

```vue
<script setup>
import { ref, onMounted } from 'vue'
import {
  fetchSunriseSunsetWithCache,
  formatDateForApi,
  getLightConditionDescription,
} from '@/services/sunriseSunset'

// Location coordinates (Warsaw, Poland)
const latitude = 52.2297
const longitude = 21.0122

// Data refs
const sunriseTime = ref('')
const sunsetTime = ref('')
const lightCondition = ref('')

onMounted(async () => {
  // Fetch data for today
  const result = await fetchSunriseSunsetWithCache(latitude, longitude)

  if ('message' in result) {
    // Handle error
    console.error('Error fetching sun data:', result.message)
  } else {
    // Set data
    sunriseTime.value = result.sunrise
    sunsetTime.value = result.sunset

    // Get human-readable light condition
    lightCondition.value = await getLightConditionDescription(latitude, longitude)
  }
})
</script>

<template>
  <div>
    <p>Sunrise: {{ sunriseTime }}</p>
    <p>Sunset: {{ sunsetTime }}</p>
    <p>Current conditions: {{ lightCondition }}</p>
  </div>
</template>
```

### API Response Data

The service returns a structured object with the following properties:

```typescript
interface SunriseSunsetResult {
  date: string // The date in format YYYY-MM-DD
  sunrise: string // Sunrise time in 24h format "05:51"
  sunset: string // Sunset time in 24h format "20:21"
  firstLight?: string // Time of first light (dawn)
  lastLight?: string // Time of last light (dusk)
  dawn?: string // Civil dawn time
  dusk?: string // Civil dusk time
  solarNoon?: string // Solar noon time
  goldenHour?: string // Golden hour time
  dayLength?: string // Duration of daylight
  timezone?: string // Timezone at the location
  utcOffset?: number // UTC offset in hours
}
```

### Error Handling

The service includes comprehensive error handling with detailed error objects:

```typescript
interface SunriseSunsetError {
  message: string // User-friendly error message
  code?: string // Error code for programmatic handling
  details?: string // Technical details useful for debugging
}
```

### Caching

The service implements a caching mechanism to reduce API calls:

- Cache TTL: 24 hours
- Cached by location (latitude/longitude) and date
- Automatically expires and refreshes stale cache entries
