{"name": "Authentication", "schema": {"required": ["accessToken", "authentication", "user"], "type": "object", "properties": {"accessToken": {"type": "string"}, "authentication": {"required": ["strategy", "accessToken", "payload"], "type": "object", "properties": {"strategy": {"type": "string"}, "accessToken": {"type": "string"}, "payload": {"type": "object", "properties": {}, "additionalProperties": true}}}, "user": {"type": "object", "properties": {}, "additionalProperties": true}}, "additionalProperties": false}}