import type { Id, NullableId, Params } from '@feathersjs/feathers'
import { KnexService } from '@feathersjs/knex'
import type { KnexAdapterParams, KnexAdapterOptions } from '@feathersjs/knex'

import type { Application } from '../../declarations'
import type { Federation, FederationData, FederationPatch, FederationQuery } from './federations.schema'

export type { Federation, FederationData, FederationPatch, FederationQuery }

export interface FederationParams extends KnexAdapterParams<FederationQuery> {}

// By default calls the standard Knex adapter service methods but can be customized with your own functionality.
export class FederationService<ServiceParams extends Params = FederationParams> extends KnexService<
  Federation,
  FederationData,
  FederationParams,
  FederationPatch
> {
    // All custom logic or overrides can be placed here
    // For now, using the base implementation per project pattern
}

// Helper to return options object for service registration
export const getOptions = (app: any) => {
    return {
        Model: app.get('postgresqlClient'),
        name: 'federations'
    }
}
