[{"schema": ":provider", "fileName": "_provider.json"}, {"schema": ":providerList", "fileName": "_providerList.json"}, {"schema": "Authentication", "fileName": "Authentication.json"}, {"schema": "AuthenticationList", "fileName": "AuthenticationList.json"}, {"schema": "AuthenticationData", "fileName": "AuthenticationData.json"}, {"schema": "AuthenticationQuery", "fileName": "AuthenticationQuery.json"}, {"schema": "MatchFormat", "fileName": "MatchFormat.json"}, {"schema": "MatchFormatList", "fileName": "MatchFormatList.json"}, {"schema": "MatchFormatData", "fileName": "MatchFormatData.json"}, {"schema": "MatchFormatQuery", "fileName": "MatchFormatQuery.json"}, {"schema": "MatchFormatPatch", "fileName": "MatchFormatPatch.json"}, {"schema": "MatchFormatPagination", "fileName": "MatchFormatPagination.json"}, {"schema": "Organizer", "fileName": "Organizer.json"}, {"schema": "OrganizerList", "fileName": "OrganizerList.json"}, {"schema": "OrganizerData", "fileName": "OrganizerData.json"}, {"schema": "OrganizerQuery", "fileName": "OrganizerQuery.json"}, {"schema": "OrganizerPatch", "fileName": "OrganizerPatch.json"}, {"schema": "OrganizerPagination", "fileName": "OrganizerPagination.json"}, {"schema": "Tournament", "fileName": "Tournament.json"}, {"schema": "TournamentList", "fileName": "TournamentList.json"}, {"schema": "TournamentData", "fileName": "TournamentData.json"}, {"schema": "<PERSON><PERSON><PERSON>y", "fileName": "TournamentQuery.json"}, {"schema": "TournamentPatch", "fileName": "TournamentPatch.json"}, {"schema": "TournamentPagination", "fileName": "TournamentPagination.json"}, {"schema": "MatchResult", "fileName": "MatchResult.json"}, {"schema": "MatchResultList", "fileName": "MatchResultList.json"}, {"schema": "MatchResultData", "fileName": "MatchResultData.json"}, {"schema": "MatchResultQuery", "fileName": "MatchResultQuery.json"}, {"schema": "MatchResultPatch", "fileName": "MatchResultPatch.json"}, {"schema": "MatchResultPagination", "fileName": "MatchResultPagination.json"}, {"schema": "Equipment", "fileName": "Equipment.json"}, {"schema": "EquipmentList", "fileName": "EquipmentList.json"}, {"schema": "EquipmentData", "fileName": "EquipmentData.json"}, {"schema": "EquipmentQuery", "fileName": "EquipmentQuery.json"}, {"schema": "EquipmentPatch", "fileName": "EquipmentPatch.json"}, {"schema": "EquipmentPagination", "fileName": "EquipmentPagination.json"}, {"schema": "Player", "fileName": "<PERSON>.json"}, {"schema": "PlayerList", "fileName": "PlayerList.json"}, {"schema": "PlayerData", "fileName": "PlayerData.json"}, {"schema": "<PERSON><PERSON><PERSON><PERSON>", "fileName": "PlayerQuery.json"}, {"schema": "PlayerPatch", "fileName": "PlayerPatch.json"}, {"schema": "PlayerPagination", "fileName": "PlayerPagination.json"}, {"schema": "Message", "fileName": "Message.json"}, {"schema": "MessageList", "fileName": "MessageList.json"}, {"schema": "MessageData", "fileName": "MessageData.json"}, {"schema": "MessageQuery", "fileName": "MessageQuery.json"}, {"schema": "MessagePatch", "fileName": "MessagePatch.json"}, {"schema": "MessagePagination", "fileName": "MessagePagination.json"}, {"schema": "Match", "fileName": "Match.json"}, {"schema": "MatchList", "fileName": "MatchList.json"}, {"schema": "MatchData", "fileName": "MatchData.json"}, {"schema": "MatchQuery", "fileName": "MatchQuery.json"}, {"schema": "MatchPatch", "fileName": "MatchPatch.json"}, {"schema": "MatchPagination", "fileName": "MatchPagination.json"}, {"schema": "User", "fileName": "User.json"}, {"schema": "UserList", "fileName": "UserList.json"}, {"schema": "UserData", "fileName": "UserData.json"}, {"schema": "UserQuery", "fileName": "UserQuery.json"}, {"schema": "UserPatch", "fileName": "UserPatch.json"}, {"schema": "UserPagination", "fileName": "UserPagination.json"}, {"schema": "MatchRegistration", "fileName": "MatchRegistration.json"}, {"schema": "MatchRegistrationList", "fileName": "MatchRegistrationList.json"}, {"schema": "MatchRegistrationData", "fileName": "MatchRegistrationData.json"}, {"schema": "MatchRegistrationQuery", "fileName": "MatchRegistrationQuery.json"}, {"schema": "MatchRegistrationPatch", "fileName": "MatchRegistrationPatch.json"}, {"schema": "MatchRegistrationPagination", "fileName": "MatchRegistrationPagination.json"}]