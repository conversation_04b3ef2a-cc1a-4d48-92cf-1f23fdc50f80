<script setup lang="ts">
import type { Match } from '@/api/feathers-client'
import MatchItem from './MatchItem.vue'
import { computed } from 'vue'

// Define props
const props = defineProps<{
  items: Match[]
  hoveredMatches?: Match[]
  currentMatch?: Match | null
}>()

// Define emits
const emit = defineEmits<{
  selectMatch: [id: number]
  hoverMatch: [id: number | null]
}>()

const handleSelectMatch = (id: number) => {
  emit('selectMatch', id)
}

const handleHoverMatch = (id: number | null) => {
  emit('hoverMatch', id)
}

// Create enhanced items with highlighting information
const enhancedItems = computed(() => {
  const hoveredIds = new Set((props.hoveredMatches || []).map(m => m.id))
  return props.items.map(item => ({
    ...item,
    isHighlighted: hoveredIds.has(item.id)
  }))
})
</script>

<template>
  <div class="flex flex-col gap-2 p-2 pt-0">
    <div v-if="!props.items || props.items.length === 0" class="p-8 text-center text-muted-foreground">
      No matches found
    </div>
    <div v-else class="space-y-2">
      <MatchItem
        v-for="item in enhancedItems"
        :key="item.id"
        :match="item"
        :is-selected="props.currentMatch?.id === item.id"
        @select="handleSelectMatch(item.id)"
        @hover="handleHoverMatch"
      />
    </div>
  </div>
</template>
