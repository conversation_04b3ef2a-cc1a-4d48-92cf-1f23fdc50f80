<script setup lang="ts">
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import { type LucideIcon } from 'lucide-vue-next'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

defineProps<{
  matchesItems: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
  }[]
  otherItems: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
  }[]
}>()

const route = useRoute()
</script>

<template>
  <SidebarGroup>
    <SidebarGroupLabel>{{ t('navigation.matches') }}</SidebarGroupLabel>
    <SidebarMenu>
      <SidebarMenuItem v-for="item in matchesItems" :key="item.title">
        <SidebarMenuButton
          as-child
          :tooltip="item.title"
          :data-active="item.url === route.path || route.path.startsWith(item.url + '/')"
        >
          <router-link :to="item.url">
            <component :is="item.icon" v-if="item.icon" />
            <span>{{ item.title }}</span>
          </router-link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarGroup>

  <SidebarGroup>
    <SidebarMenu>
      <SidebarMenuItem v-for="item in otherItems" :key="item.title">
        <SidebarMenuButton
          as-child
          :tooltip="item.title"
          :data-active="item.url === route.path || route.path.startsWith(item.url + '/')"
        >
          <router-link :to="item.url">
            <component :is="item.icon" v-if="item.icon" />
            <span>{{ item.title }}</span>
          </router-link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  </SidebarGroup>
</template>
