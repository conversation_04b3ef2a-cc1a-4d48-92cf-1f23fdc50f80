# $populate Parameter Usage Example

This document demonstrates how to use the `$populate` parameter in the match-registrations service to fetch related player data.

## Basic Usage

### Find registrations with populated player data

```javascript
// Client-side usage
const registrations = await app.service('match-registrations').find({
  query: {
    matchId: 123,
    $populate: ['player']
  }
})

// Result will include player data:
// {
//   data: [
//     {
//       id: 1,
//       matchId: 123,
//       playerId: 456,
//       status: 'confirmed',
//       styleDivision: 'recurve',
//       ageDivision: 'senior',
//       genderDivision: 'male',
//       player: {
//         id: 456,
//         firstname: '<PERSON>',
//         lastname: '<PERSON>e',
//         sex: 'male',
//         city: 'New York',
//         country: 'USA'
//       }
//     }
//   ]
// }
```

### Get a single registration with populated player data

```javascript
const registration = await app.service('match-registrations').get(1, {
  query: {
    $populate: ['player']
  }
})

// Result includes player data:
// {
//   id: 1,
//   matchId: 123,
//   playerId: 456,
//   status: 'confirmed',
//   player: {
//     id: 456,
//     firstname: '<PERSON>',
//     lastname: 'Doe',
//     sex: 'male',
//     city: 'New York',
//     country: 'USA'
//   }
// }
```

## Without $populate

```javascript
// Without $populate, only the playerId is returned
const registration = await app.service('match-registrations').get(1)

// Result:
// {
//   id: 1,
//   matchId: 123,
//   playerId: 456,
//   status: 'confirmed'
//   // No player object
// }
```

## Selected Fields

The populate functionality automatically selects only specific fields from the player service:
- `id`
- `firstname`
- `lastname`
- `sex`
- `city`
- `country`

This helps reduce the response size and protects sensitive information like `userId`.

## Implementation Details

The `$populate` parameter is implemented using a custom hook that:

1. Extracts the `$populate` parameter from the query in the before hook
2. Removes it from the query to prevent database errors
3. Processes the population in the after hook by:
   - Collecting all foreign keys from the results
   - Fetching related records in batches
   - Mapping the related data back to the original results

This approach is efficient and works with both paginated and non-paginated results.
