import { BadRequest } from '@feathersjs/errors'

import type { HookContext } from '../../../declarations'
import type { MatchResultData } from '../match-results.schema'

/**
 * Hook to validate match result data
 * - Checks if the match exists and is active
 * - Checks if the player exists
 * - Checks if the player is registered for the match
 * - Checks if the player already has a result for this match
 */
export const validateMatchResult = () => {
  return async (context: HookContext) => {
    const { app, data, id, method } = context
    const resultData = data as MatchResultData

    // For patch operations, we need to get the existing record first
    if (method === 'patch' && id !== undefined) {
      const existingResult = await app.service('match-results').get(id)

      // If patching, use the existing matchId and playerId
      if (!resultData.matchId) {
        resultData.matchId = existingResult.matchId
      }

      if (!resultData.playerId) {
        resultData.playerId = existingResult.playerId
      }
    }

    // Validate match exists and is active
    try {
      const match = await app.service('matches').get(resultData.matchId)

      if (!match.isActive) {
        throw new BadRequest('This match is not active')
      }
    } catch (error: any) {
      if (error.name === 'NotFound') {
        throw new BadRequest('Match not found')
      }
      throw error
    }

    // Validate player exists
    try {
      await app.service('players').get(resultData.playerId)
    } catch (error: any) {
      if (error.name === 'NotFound') {
        throw new BadRequest('Player not found')
      }
      throw error
    }

    // Check if the player is registered for the match
    const registrations = await app.service('match-registrations').find({
      query: {
        matchId: resultData.matchId,
        playerId: resultData.playerId
      }
    })

    if (registrations.total === 0) {
      throw new BadRequest('Player is not registered for this match')
    }

    // For create operations, check if the player already has a result for this match
    if (method === 'create') {
      const existingResults = await app.service('match-results').find({
        query: {
          matchId: resultData.matchId,
          playerId: resultData.playerId
        }
      })

      if (existingResults.total > 0) {
        throw new BadRequest('Player already has a result for this match')
      }
    }

    // Validate points and maxPoints
    /* if (resultData.points !== undefined && resultData.points < 0) {
      throw new BadRequest('Points cannot be negative')
    } */

    if (resultData.maxPoints !== undefined && resultData.maxPoints <= 0) {
      throw new BadRequest('Max points must be greater than zero')
    }

    if (resultData.points !== undefined && resultData.maxPoints !== undefined && resultData.points > resultData.maxPoints) {
      throw new BadRequest('Points cannot be greater than max points')
    }

    // Validate place
    if (resultData.place !== undefined && resultData.place <= 0) {
      throw new BadRequest('Place must be greater than zero')
    }

    return context
  }
}
